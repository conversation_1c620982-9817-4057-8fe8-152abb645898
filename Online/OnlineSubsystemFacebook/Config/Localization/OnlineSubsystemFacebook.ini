[CommonSettings]
SourcePath=Plugins/Online/OnlineSubsystemFacebook/Content/Localization/OnlineSubsystemFacebook
DestinationPath=Plugins/Online/OnlineSubsystemFacebook/Content/Localization/OnlineSubsystemFacebook
ManifestName=OnlineSubsystemFacebook.manifest
ArchiveName=OnlineSubsystemFacebook.archive
PortableObjectName=OnlineSubsystemFacebook.po
ResourceName=OnlineSubsystemFacebook.locres
NativeCulture=en
CulturesToGenerate=en
CulturesToGenerate=fr
CulturesToGenerate=de
CulturesToGenerate=es
CulturesToGenerate=es-419
CulturesToGenerate=it
CulturesToGenerate=ja
CulturesToGenerate=ko
CulturesToGenerate=ru
CulturesToGenerate=zh-Hans
CulturesToGenerate=ar
CulturesToGenerate=pl
CulturesToGenerate=pt-BR

;Gather text from source code
[GatherTextStep0]
CommandletClass=GatherTextFromSource
SearchDirectoryPaths=Plugins/Online/OnlineSubsystemFacebook/Source/
FileNameFilters=*.cpp
FileNameFilters=*.h
FileNameFilters=*.c
FileNameFilters=*.inl
FileNameFilters=*.mm
FileNameFilters=*.ini
ShouldGatherFromEditorOnlyData=false

;Write Manifest
[GatherTextStep1]
CommandletClass=GenerateGatherManifest

;Write Archives
[GatherTextStep2]
CommandletClass=GenerateGatherArchive

;Import localized PO files
[GatherTextStep3]
CommandletClass=InternationalizationExport
bImportLoc=true

;Write Localized Text Resource
[GatherTextStep4]
CommandletClass=GenerateTextLocalizationResource

;Export  PO files
[GatherTextStep5]
CommandletClass=InternationalizationExport
bExportLoc=true