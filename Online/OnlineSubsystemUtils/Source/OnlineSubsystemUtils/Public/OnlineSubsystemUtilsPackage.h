// Copyright Epic Games, Inc. All Rights Reserved.

// Can't be #pragma once because other modules may define PACKAGE_SCOPE

// Intended to be the last include in an exported class definition
// <PERSON>perly defines some members as "public to the module" vs "private to the consumer/user"

// [[ IncludeTool: Inline ]] // Markup to tell <PERSON>ludeTool that this file is state changing and cannot be optimized out.

#undef PACKAGE_SCOPE
#ifdef ONLINESUBSYSTEMUTILS_PACKAGE
#define PACKAGE_SCOPE public
#else
#define PACKAGE_SCOPE protected
#endif
