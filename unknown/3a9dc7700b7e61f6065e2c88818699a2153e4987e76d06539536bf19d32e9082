# Dotnet code style settings:
[*.cs]


# CA1034: Nested types should not be visible
dotnet_diagnostic.CA1034.severity = none

# CA1819: Properties should not return arrays
dotnet_diagnostic.CA1819.severity = none

# CA1051: Do not declare visible instance fields
dotnet_diagnostic.CA1051.severity = none

# IDE0025: Use expression body for properties
dotnet_diagnostic.IDE0025.severity = none

# CA5350: Do Not Use Weak Cryptographic Algorithms
dotnet_diagnostic.CA5350.severity = none

# CA2234: Pass system uri objects instead of strings
dotnet_diagnostic.CA2234.severity = none

# IDE0049: Use framework type
dotnet_diagnostic.IDE0049.severity = none

# IDE1006: Naming Styles
dotnet_diagnostic.IDE1006.severity = none

# CA2227: Collection properties should be read only
dotnet_diagnostic.CA2227.severity = none

# CA1054: URI-like parameters should not be strings
dotnet_diagnostic.CA1054.severity = none

# CA1307: Specify StringComparison for clarity
dotnet_diagnostic.CA1307.severity = none

# CA1056: URI-like properties should not be strings
dotnet_diagnostic.CA1056.severity = none

# CA1849: Call async methods when in an async method
dotnet_diagnostic.CA1849.severity = none
