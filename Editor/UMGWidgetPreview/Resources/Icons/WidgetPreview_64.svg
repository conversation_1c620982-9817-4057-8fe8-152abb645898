<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 28.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 64 64" style="enable-background:new 0 0 64 64;" xml:space="preserve">
<style type="text/css">
	.st0{fill:url(#SVGID_1_);}
	.st1{opacity:0.7;fill:#FFFFFF;enable-background:new    ;}
	.st2{fill:none;}
	.st3{fill:url(#SVGID_00000075871795383507694380000001061014108332467616_);}
</style>
<g id="UserWidget_x5F_64">
	
		<linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="31.95" y1="51.4099" x2="31.95" y2="16.4814" gradientTransform="matrix(1 0 0 -1 0 66)">
		<stop  offset="0" style="stop-color:#808080"/>
		<stop  offset="0.591" style="stop-color:#505050"/>
		<stop  offset="1" style="stop-color:#333333"/>
	</linearGradient>
	<path class="st0" d="M54.2,13.5H9.7v36h44.5V13.5z"/>
	<path class="st1" d="M52.6,14H24.3c-0.5,0-0.9,0.3-0.9,0.8v5.7c0,0.4,0.4,0.8,0.9,0.8h28.3c0.5,0,0.9-0.3,0.9-0.8v-5.7
		C53.5,14.4,53.1,14,52.6,14z"/>
	<path class="st1" d="M17.6,14h-6.3c-0.5,0-0.9,0.4-0.9,0.9V48c0,0.5,0.4,0.9,0.9,0.9h6.3c0.5,0,0.9-0.4,0.9-0.9V14.9
		C18.4,14.4,18,14,17.6,14z"/>
	<rect class="st2" width="64" height="64"/>
</g>
<g id="Layer_2">
	<path id="path52" class="st1" d="M41.8,48.8l11.4-7.9c0.4-0.3,0.4-1,0-1.3l-11.4-8c-0.4-0.3-0.9,0.1-0.9,0.7v15.8
		C41,48.7,41.4,49.1,41.8,48.8z"/>
	<path class="st1" d="M53.5,29v-1.1c0-0.5-0.4-0.9-0.8-0.9h-6.2c-0.5,0-0.8,0.4-0.8,0.9V29H53.5z"/>
	
		<linearGradient id="SVGID_00000093881018302669878440000007325648208988380316_" gradientUnits="userSpaceOnUse" x1="30.175" y1="29.0805" x2="30.175" y2="44.9026">
		<stop  offset="0" style="stop-color:#B3B3B3"/>
		<stop  offset="1" style="stop-color:#4D4D4D"/>
	</linearGradient>
	<path style="fill:url(#SVGID_00000093881018302669878440000007325648208988380316_);" d="M38.5,29.2c-2-0.3-4.5,0.7-6.5,3.6
		c-4.1-6.1-10.1-4.1-10.1,1c0,5.1,6.1,5.6,10.1,11.1c2-2.7,4.5-4.3,6.5-5.7V29.2z"/>
</g>
</svg>
