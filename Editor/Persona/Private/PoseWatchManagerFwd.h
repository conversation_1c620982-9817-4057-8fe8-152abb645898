// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Misc/CoreDelegates.h"

class IPoseWatchManager;
class IPoseWatchManagerColumn;

class UPoseWatch;
class UPoseWatchFolder;

struct FPoseWatchManagerInitializationOptions;

struct IPoseWatchManagerTreeItem;
struct FPoseWatchManagerPoseWatchTreeItem;
struct FPoseWatchManagerFolderTreeItem;

struct FObjectKey;


struct FPoseWatchManagerDragDropPayload;
struct FPoseWatchManagerDragValidationInfo;