// Copyright Epic Games, Inc. All Rights Reserved.

#include "MaterialEditorTabs.h"

#include "HAL/Platform.h"
#include "UObject/NameTypes.h"

const FName FMaterialEditorTabs::PreviewTabId(TEXT("MaterialEditor_Preview"));
const FName FMaterialEditorTabs::PropertiesTabId(TEXT("MaterialEditor_MaterialProperties"));
const FName FMaterialEditorTabs::PaletteTabId(TEXT("MaterialEditor_Palette"));
const FName FMaterialEditorTabs::FindTabId(TEXT("MaterialEditor_Find"));
const FName FMaterialEditorTabs::GraphEditor(TEXT("Document"));
const FName FMaterialEditorTabs::PreviewSettingsTabId(TEXT("MaterialEditor_PreviewSettings"));
const FName FMaterialEditorTabs::ParameterDefaultsTabId(TEXT("MaterialEditor_ParameterDefaults"));
const FName FMaterialEditorTabs::CustomPrimitiveTabId(TEXT("MaterialEditor_CustomPrimitiveData"));
const FName FMaterialEditorTabs::LayerPropertiesTabId(TEXT("MaterialInstanceEditor_MaterialLayerProperties"));
const FName FMaterialEditorTabs::SubstrateTabId(TEXT("MaterialEditor_Substrate"));