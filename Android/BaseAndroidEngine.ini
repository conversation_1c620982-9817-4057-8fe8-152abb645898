[OnlineSubsystem]
DefaultPlatformService=GooglePlay
NativePlatformService=GooglePlay
LocalPlatformName=AND

[OnlineSubsystemGooglePlay.Store]
bSupportsInAppPurchasing=true

[Audio]
AudioCaptureModuleName=AudioCaptureAndroid

[OnlineServices.EOS.Auth]
DefaultExternalCredentialTypeStr=Google

;Uncomment to turn on AndroidBackgroundHttp through the AndroidFetchBackgroundDownload and AndroidBackgroundService modules
;[BackgroundHttp]
;PlatformModularFeatureName=AndroidFetchBackgroundDownload

[BuildPatchTool]
VerificationThreadCount=3

[/Script/Engine.GarbageCollectionSettings]
gc.MaxObjectsInGame=131072

[SlateRenderer]
NumPreallocatedVertices=200

[/Script/Engine.RendererSettings]
r.Substrate.RoughDiffuse=0
r.Substrate.ShadingQuality=2
r.Substrate.TileCoord8bits=1
r.Substrate.SheenQuality=2
r.Substrate.Glints=0
r.Substrate.SpecularProfile=0
r.Substrate.ClosuresPerPixel=1