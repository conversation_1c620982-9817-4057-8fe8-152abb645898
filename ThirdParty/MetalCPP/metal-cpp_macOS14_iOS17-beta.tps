<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>MetalCPP</Name>
  <!-- Software Name and Version  -->
<!-- Software Name:MetalCPP
    Version:https://developer.apple.com/metal/cpp/files/metal-cpp_macOS14_iOS17-beta.zip -->
<!-- Notes:
 -->
  <Location></Location>
  <Function>The library is a C++ wrapper for the Metal language objective-c implementation provided by Apple. It enables us to use a more C++ API while maintaining API parity and performance. mtlpp is the current library in use for this task and it will be removed.</Function>
  <Eula>https://developer.apple.com/metal/cpp/files/metal-cpp_macOS14_iOS17-beta.zip</Eula>
  <RedistributeTo>
    <EndUserGroup>Licencees</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
     <EndUserGroup>Git</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses</LicenseFolder>
</TpsData>
 