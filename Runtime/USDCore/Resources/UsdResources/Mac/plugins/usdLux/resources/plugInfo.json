# Portions of this file auto-generated by usdGenSchema.
# Edits will survive regeneration except for comments and
# changes to types with autoGenerated=true.
{
    "Plugins": [
        {
            "Info": {
                "Types": {
                    "UsdLuxBoundableLightBase": {
                        "alias": {
                            "UsdSchemaBase": "BoundableLightBase"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomBoundable"
                        ], 
                        "schemaKind": "abstractTyped"
                    }, 
                    "UsdLuxCylinderLight": {
                        "alias": {
                            "UsdSchemaBase": "CylinderLight"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdLuxBoundableLightBase"
                        ], 
                        "implementsComputeExtent": true, 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdLuxDiskLight": {
                        "alias": {
                            "UsdSchemaBase": "DiskLight"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdLuxBoundableLightBase"
                        ], 
                        "implementsComputeExtent": true, 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdLuxDistantLight": {
                        "alias": {
                            "UsdSchemaBase": "DistantLight"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdLuxNonboundableLightBase"
                        ], 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdLuxDomeLight": {
                        "alias": {
                            "UsdSchemaBase": "DomeLight"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdLuxNonboundableLightBase"
                        ], 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdLuxDomeLight_1": {
                        "alias": {
                            "UsdSchemaBase": "DomeLight_1"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdLuxNonboundableLightBase"
                        ], 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdLuxGeometryLight": {
                        "alias": {
                            "UsdSchemaBase": "GeometryLight"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdLuxNonboundableLightBase"
                        ], 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdLuxLightAPI": {
                        "alias": {
                            "UsdSchemaBase": "LightAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "providesUsdShadeConnectableAPIBehavior": true, 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "UsdLuxLightFilter": {
                        "alias": {
                            "UsdSchemaBase": "LightFilter"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomXformable"
                        ], 
                        "providesUsdShadeConnectableAPIBehavior": true, 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdLuxLightListAPI": {
                        "alias": {
                            "UsdSchemaBase": "LightListAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "UsdLuxListAPI": {
                        "alias": {
                            "UsdSchemaBase": "ListAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "UsdLuxMeshLightAPI": {
                        "alias": {
                            "UsdSchemaBase": "MeshLightAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "UsdLuxNonboundableLightBase": {
                        "alias": {
                            "UsdSchemaBase": "NonboundableLightBase"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomXformable"
                        ], 
                        "schemaKind": "abstractTyped"
                    }, 
                    "UsdLuxPluginLight": {
                        "alias": {
                            "UsdSchemaBase": "PluginLight"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomXformable"
                        ], 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdLuxPluginLightFilter": {
                        "alias": {
                            "UsdSchemaBase": "PluginLightFilter"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdLuxLightFilter"
                        ], 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdLuxPortalLight": {
                        "alias": {
                            "UsdSchemaBase": "PortalLight"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdLuxBoundableLightBase"
                        ], 
                        "implementsComputeExtent": true, 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdLuxRectLight": {
                        "alias": {
                            "UsdSchemaBase": "RectLight"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdLuxBoundableLightBase"
                        ], 
                        "implementsComputeExtent": true, 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdLuxShadowAPI": {
                        "alias": {
                            "UsdSchemaBase": "ShadowAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "UsdLuxShapingAPI": {
                        "alias": {
                            "UsdSchemaBase": "ShapingAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "UsdLuxSphereLight": {
                        "alias": {
                            "UsdSchemaBase": "SphereLight"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdLuxBoundableLightBase"
                        ], 
                        "implementsComputeExtent": true, 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdLuxVolumeLightAPI": {
                        "alias": {
                            "UsdSchemaBase": "VolumeLightAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "UsdLux_DiscoveryPlugin": {
                        "bases": [
                            "NdrDiscoveryPlugin"
                        ]
                    }, 
                    "UsdLux_LightDefParserPlugin": {
                        "bases": [
                            "NdrParserPlugin"
                        ]
                    }
                }
            }, 
            "LibraryPath": "../../../../../Source/ThirdParty/Mac/bin/libusd_usdLux.dylib", 
            "Name": "usdLux", 
            "ResourcePath": "resources", 
            "Root": "..", 
            "Type": "library"
        }
    ]
}
