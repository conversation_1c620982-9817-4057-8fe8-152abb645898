// Copyright Epic Games, Inc. All Rights Reserved.
#pragma once

#include "Chaos/Core.h"
#include "Chaos/Declares.h"
#include "Chaos/GeometryParticlesfwd.h"
#include "Chaos/Island/IslandManagerFwd.h"
#include "Misc/Build.h"

namespace Chaos
{
	class FConstraintHandle;

	class FPBDCollisionConstraints;

	class FPBDCollisionConstraintHandle;

	class FPBDConstraintColor;

	class FPBDJointConstraintHandle;

	class FPBDJointConstraints;

	class FSimulationSpace;

	class FCharacterGroundConstraintContainer;
}
