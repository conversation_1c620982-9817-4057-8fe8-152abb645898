// Copyright Epic Games, Inc. All Rights Reserved.
// generated
#ifndef _SYMS_META_EVAL_C
#define _SYMS_META_EVAL_C
//~ generated from code at syms/metaprogram/syms_metaprogram_eval.c:367
SYMS_GLOBAL SYMS_U8 syms_eval_opcode_ctrlbits[] = {
0x00,0x14,0x10,0x15,0x41,0x05,0x11,0x41,0x21,0x00,0x00,0x11,0x21,0x41,0x81,0x15,
0x15,0x19,0x19,0x19,0x19,0x19,0x19,0x19,0x19,0x19,0x19,0x15,0x19,0x19,0x15,0x19,
0x19,0x19,0x19,0x19,0x19,0x15,0x15,0x25,0x11,0x04,0x10,
};
//~ generated from code at syms/metaprogram/syms_metaprogram_eval.c:414
SYMS_GLOBAL SYMS_U8 syms_eval_type_group_from_type_kind_table[] = {
0,0,2,2,2,2,2,0,0,1,1,1,1,1,0,0,1,0,3,0,0,4,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,1,0,1,0,0,0,0,0,0,0,0,
};
//~ generated from code at syms/metaprogram/syms_metaprogram_eval.c:442
SYMS_GLOBAL SYMS_String8 syms_eval_conversion_string_table[] = {
syms_str8_comp(""),
syms_str8_comp(""),
syms_str8_comp("cannot convert between these types"),
syms_str8_comp("cannot convert to this type"),
syms_str8_comp("cannot convert this type"),
};
//~ generated from code at syms/metaprogram/syms_metaprogram_eval.c:508
SYMS_GLOBAL SYMS_U8 syms_eval_conversion_rule_table[] = {
2,4,4,4,4,
3,0,0,1,1,
3,0,0,1,1,
3,1,1,0,1,
3,1,1,1,0,
};
#endif
