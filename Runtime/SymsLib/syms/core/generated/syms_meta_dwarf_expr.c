// Copyright Epic Games, Inc. All Rights Reserved.
// generated
#ifndef _SYMS_META_DWARF_EXPR_C
#define _SYMS_META_DWARF_EXPR_C
//~ generated from code at syms/metaprogram/syms_metaprogram_serial.c:1150
SYMS_API SYMS_RegID
syms_reg_from_dw_reg_x86(SYMS_DwRegX86 v){
SYMS_RegID result = 0;
switch (v){
default: break;
case SYMS_DwRegX86_EAX: result = SYMS_RegX86Code_eax; break;
case SYMS_DwRegX86_ECX: result = SYMS_RegX86Code_ecx; break;
case SYMS_DwRegX86_EDX: result = SYMS_RegX86Code_edx; break;
case SYMS_DwRegX86_EBX: result = SYMS_RegX86Code_ebx; break;
case SYMS_DwRegX86_ESP: result = SYMS_RegX86Code_esp; break;
case SYMS_DwRegX86_EBP: result = SYMS_RegX86Code_ebp; break;
case SYMS_DwRegX86_ESI: result = SYMS_RegX86Code_esi; break;
case SYMS_DwRegX86_EDI: result = SYMS_RegX86Code_edi; break;
case SYMS_DwRegX86_EIP: result = SYMS_RegX86Code_eip; break;
case SYMS_DwRegX86_EFLAGS: result = SYMS_RegX86Code_eflags; break;
case SYMS_DwRegX86_ST0: result = SYMS_RegX86Code_st0; break;
case SYMS_DwRegX86_ST1: result = SYMS_RegX86Code_st1; break;
case SYMS_DwRegX86_ST2: result = SYMS_RegX86Code_st2; break;
case SYMS_DwRegX86_ST3: result = SYMS_RegX86Code_st3; break;
case SYMS_DwRegX86_ST4: result = SYMS_RegX86Code_st4; break;
case SYMS_DwRegX86_ST5: result = SYMS_RegX86Code_st5; break;
case SYMS_DwRegX86_ST6: result = SYMS_RegX86Code_st6; break;
case SYMS_DwRegX86_ST7: result = SYMS_RegX86Code_st7; break;
case SYMS_DwRegX86_XMM0: result = SYMS_RegX86Code_xmm0; break;
case SYMS_DwRegX86_XMM1: result = SYMS_RegX86Code_xmm1; break;
case SYMS_DwRegX86_XMM2: result = SYMS_RegX86Code_xmm2; break;
case SYMS_DwRegX86_XMM3: result = SYMS_RegX86Code_xmm3; break;
case SYMS_DwRegX86_XMM4: result = SYMS_RegX86Code_xmm4; break;
case SYMS_DwRegX86_XMM5: result = SYMS_RegX86Code_xmm5; break;
case SYMS_DwRegX86_XMM6: result = SYMS_RegX86Code_xmm6; break;
case SYMS_DwRegX86_XMM7: result = SYMS_RegX86Code_xmm7; break;
case SYMS_DwRegX86_MM0: result = SYMS_RegX86Code_mm0; break;
case SYMS_DwRegX86_MM1: result = SYMS_RegX86Code_mm1; break;
case SYMS_DwRegX86_MM2: result = SYMS_RegX86Code_mm2; break;
case SYMS_DwRegX86_MM3: result = SYMS_RegX86Code_mm3; break;
case SYMS_DwRegX86_MM4: result = SYMS_RegX86Code_mm4; break;
case SYMS_DwRegX86_MM5: result = SYMS_RegX86Code_mm5; break;
case SYMS_DwRegX86_MM6: result = SYMS_RegX86Code_mm6; break;
case SYMS_DwRegX86_MM7: result = SYMS_RegX86Code_mm7; break;
case SYMS_DwRegX86_FCW: result = SYMS_RegX86Code_fcw; break;
case SYMS_DwRegX86_FSW: result = SYMS_RegX86Code_fsw; break;
case SYMS_DwRegX86_MXCSR: result = SYMS_RegX86Code_mxcsr; break;
case SYMS_DwRegX86_ES: result = SYMS_RegX86Code_es; break;
case SYMS_DwRegX86_CS: result = SYMS_RegX86Code_cs; break;
case SYMS_DwRegX86_SS: result = SYMS_RegX86Code_ss; break;
case SYMS_DwRegX86_DS: result = SYMS_RegX86Code_ds; break;
case SYMS_DwRegX86_FS: result = SYMS_RegX86Code_fs; break;
case SYMS_DwRegX86_GS: result = SYMS_RegX86Code_gs; break;
}
return(result);
}
SYMS_API SYMS_RegID
syms_reg_from_dw_reg_x64(SYMS_DwRegX64 v){
SYMS_RegID result = 0;
switch (v){
default: break;
case SYMS_DwRegX64_RAX: result = SYMS_RegX64Code_rax; break;
case SYMS_DwRegX64_RDX: result = SYMS_RegX64Code_rdx; break;
case SYMS_DwRegX64_RCX: result = SYMS_RegX64Code_rcx; break;
case SYMS_DwRegX64_RBX: result = SYMS_RegX64Code_rbx; break;
case SYMS_DwRegX64_RSI: result = SYMS_RegX64Code_rsi; break;
case SYMS_DwRegX64_RDI: result = SYMS_RegX64Code_rdi; break;
case SYMS_DwRegX64_RBP: result = SYMS_RegX64Code_rbp; break;
case SYMS_DwRegX64_RSP: result = SYMS_RegX64Code_rsp; break;
case SYMS_DwRegX64_R8: result = SYMS_RegX64Code_r8; break;
case SYMS_DwRegX64_R9: result = SYMS_RegX64Code_r9; break;
case SYMS_DwRegX64_R10: result = SYMS_RegX64Code_r10; break;
case SYMS_DwRegX64_R11: result = SYMS_RegX64Code_r11; break;
case SYMS_DwRegX64_R12: result = SYMS_RegX64Code_r12; break;
case SYMS_DwRegX64_R13: result = SYMS_RegX64Code_r13; break;
case SYMS_DwRegX64_R14: result = SYMS_RegX64Code_r14; break;
case SYMS_DwRegX64_R15: result = SYMS_RegX64Code_r15; break;
case SYMS_DwRegX64_RIP: result = SYMS_RegX64Code_rip; break;
case SYMS_DwRegX64_XMM0: result = SYMS_RegX64Code_xmm0; break;
case SYMS_DwRegX64_XMM1: result = SYMS_RegX64Code_xmm1; break;
case SYMS_DwRegX64_XMM2: result = SYMS_RegX64Code_xmm2; break;
case SYMS_DwRegX64_XMM3: result = SYMS_RegX64Code_xmm3; break;
case SYMS_DwRegX64_XMM4: result = SYMS_RegX64Code_xmm4; break;
case SYMS_DwRegX64_XMM5: result = SYMS_RegX64Code_xmm5; break;
case SYMS_DwRegX64_XMM6: result = SYMS_RegX64Code_xmm6; break;
case SYMS_DwRegX64_XMM7: result = SYMS_RegX64Code_xmm7; break;
case SYMS_DwRegX64_XMM8: result = SYMS_RegX64Code_xmm8; break;
case SYMS_DwRegX64_XMM9: result = SYMS_RegX64Code_xmm9; break;
case SYMS_DwRegX64_XMM10: result = SYMS_RegX64Code_xmm10; break;
case SYMS_DwRegX64_XMM11: result = SYMS_RegX64Code_xmm11; break;
case SYMS_DwRegX64_XMM12: result = SYMS_RegX64Code_xmm12; break;
case SYMS_DwRegX64_XMM13: result = SYMS_RegX64Code_xmm13; break;
case SYMS_DwRegX64_XMM14: result = SYMS_RegX64Code_xmm14; break;
case SYMS_DwRegX64_XMM15: result = SYMS_RegX64Code_xmm15; break;
case SYMS_DwRegX64_ST0: result = SYMS_RegX64Code_st0; break;
case SYMS_DwRegX64_ST1: result = SYMS_RegX64Code_st1; break;
case SYMS_DwRegX64_ST2: result = SYMS_RegX64Code_st2; break;
case SYMS_DwRegX64_ST3: result = SYMS_RegX64Code_st3; break;
case SYMS_DwRegX64_ST4: result = SYMS_RegX64Code_st4; break;
case SYMS_DwRegX64_ST5: result = SYMS_RegX64Code_st5; break;
case SYMS_DwRegX64_ST6: result = SYMS_RegX64Code_st6; break;
case SYMS_DwRegX64_ST7: result = SYMS_RegX64Code_st7; break;
case SYMS_DwRegX64_MM0: result = SYMS_RegX64Code_mm0; break;
case SYMS_DwRegX64_MM1: result = SYMS_RegX64Code_mm1; break;
case SYMS_DwRegX64_MM2: result = SYMS_RegX64Code_mm2; break;
case SYMS_DwRegX64_MM3: result = SYMS_RegX64Code_mm3; break;
case SYMS_DwRegX64_MM4: result = SYMS_RegX64Code_mm4; break;
case SYMS_DwRegX64_MM5: result = SYMS_RegX64Code_mm5; break;
case SYMS_DwRegX64_MM6: result = SYMS_RegX64Code_mm6; break;
case SYMS_DwRegX64_MM7: result = SYMS_RegX64Code_mm7; break;
case SYMS_DwRegX64_RFLAGS: result = SYMS_RegX64Code_rflags; break;
case SYMS_DwRegX64_ES: result = SYMS_RegX64Code_es; break;
case SYMS_DwRegX64_CS: result = SYMS_RegX64Code_cs; break;
case SYMS_DwRegX64_SS: result = SYMS_RegX64Code_ss; break;
case SYMS_DwRegX64_DS: result = SYMS_RegX64Code_ds; break;
case SYMS_DwRegX64_FS: result = SYMS_RegX64Code_fs; break;
case SYMS_DwRegX64_GS: result = SYMS_RegX64Code_gs; break;
}
return(result);
}
SYMS_API SYMS_DwVersion
syms_dw_version_from_op_code(SYMS_DwOp v){
SYMS_DwVersion result = SYMS_DwVersion_Null;
switch (v){
default: break;
case SYMS_DwOp_ADDR: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_DEREF: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_CONST1U: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_CONST1S: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_CONST2U: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_CONST2S: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_CONST4U: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_CONST4S: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_CONST8U: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_CONST8S: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_CONSTU: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_CONSTS: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_DUP: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_DROP: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_OVER: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_PICK: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_SWAP: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_ROT: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_XDEREF: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_ABS: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_AND: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_DIV: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_MINUS: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_MOD: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_MUL: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_NEG: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_NOT: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_OR: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_PLUS: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_PLUS_UCONST: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_SHL: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_SHR: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_SHRA: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_XOR: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_SKIP: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_BRA: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_EQ: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_GE: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_GT: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_LE: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_LT: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_NE: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_LIT0: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_LIT1: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_LIT2: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_LIT3: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_LIT4: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_LIT5: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_LIT6: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_LIT7: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_LIT8: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_LIT9: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_LIT10: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_LIT11: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_LIT12: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_LIT13: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_LIT14: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_LIT15: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_LIT16: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_LIT17: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_LIT18: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_LIT19: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_LIT20: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_LIT21: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_LIT22: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_LIT23: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_LIT24: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_LIT25: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_LIT26: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_LIT27: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_LIT28: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_LIT29: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_LIT30: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_LIT31: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_REG0: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_REG1: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_REG2: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_REG3: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_REG4: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_REG5: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_REG6: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_REG7: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_REG8: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_REG9: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_REG10: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_REG11: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_REG12: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_REG13: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_REG14: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_REG15: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_REG16: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_REG17: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_REG18: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_REG19: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_REG20: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_REG21: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_REG22: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_REG23: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_REG24: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_REG25: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_REG26: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_REG27: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_REG28: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_REG29: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_REG30: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_REG31: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_BREG0: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_BREG1: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_BREG2: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_BREG3: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_BREG4: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_BREG5: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_BREG6: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_BREG7: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_BREG8: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_BREG9: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_BREG10: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_BREG11: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_BREG12: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_BREG13: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_BREG14: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_BREG15: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_BREG16: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_BREG17: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_BREG18: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_BREG19: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_BREG20: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_BREG21: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_BREG22: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_BREG23: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_BREG24: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_BREG25: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_BREG26: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_BREG27: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_BREG28: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_BREG29: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_BREG30: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_BREG31: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_REGX: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_FBREG: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_BREGX: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_PIECE: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_DEREF_SIZE: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_XDEREF_SIZE: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_NOP: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_PUSH_OBJECT_ADDRESS: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_CALL2: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_CALL4: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_CALL_REF: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_FORM_TLS_ADDRESS: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_CALL_FRAME_CFA: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_BIT_PIECE: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_IMPLICIT_VALUE: result = SYMS_DwVersion_V4; break;
case SYMS_DwOp_STACK_VALUE: result = SYMS_DwVersion_V4; break;
case SYMS_DwOp_IMPLICIT_POINTER: result = SYMS_DwVersion_V5; break;
case SYMS_DwOp_ADDRX: result = SYMS_DwVersion_V5; break;
case SYMS_DwOp_CONSTX: result = SYMS_DwVersion_V5; break;
case SYMS_DwOp_ENTRY_VALUE: result = SYMS_DwVersion_V5; break;
case SYMS_DwOp_CONST_TYPE: result = SYMS_DwVersion_V5; break;
case SYMS_DwOp_REGVAL_TYPE: result = SYMS_DwVersion_V5; break;
case SYMS_DwOp_DEREF_TYPE: result = SYMS_DwVersion_V5; break;
case SYMS_DwOp_XDEREF_TYPE: result = SYMS_DwVersion_V5; break;
case SYMS_DwOp_CONVERT: result = SYMS_DwVersion_V5; break;
case SYMS_DwOp_REINTERPRET: result = SYMS_DwVersion_V5; break;
case SYMS_DwOp_GNU_PUSH_TLS_ADDRESS: result = SYMS_DwVersion_V3; break;
case SYMS_DwOp_GNU_UNINIT: result = SYMS_DwVersion_V3; break;
}
return(result);
}
SYMS_API SYMS_U64
syms_dw_num_operands_from_op_code(SYMS_DwOp v){
SYMS_U64 result = 0;
switch (v){
default: break;
case SYMS_DwOp_ADDR: result = 1; break;
case SYMS_DwOp_DEREF: result = 0; break;
case SYMS_DwOp_CONST1U: result = 1; break;
case SYMS_DwOp_CONST1S: result = 1; break;
case SYMS_DwOp_CONST2U: result = 1; break;
case SYMS_DwOp_CONST2S: result = 1; break;
case SYMS_DwOp_CONST4U: result = 1; break;
case SYMS_DwOp_CONST4S: result = 1; break;
case SYMS_DwOp_CONST8U: result = 1; break;
case SYMS_DwOp_CONST8S: result = 1; break;
case SYMS_DwOp_CONSTU: result = 1; break;
case SYMS_DwOp_CONSTS: result = 1; break;
case SYMS_DwOp_DUP: result = 0; break;
case SYMS_DwOp_DROP: result = 0; break;
case SYMS_DwOp_OVER: result = 0; break;
case SYMS_DwOp_PICK: result = 1; break;
case SYMS_DwOp_SWAP: result = 0; break;
case SYMS_DwOp_ROT: result = 0; break;
case SYMS_DwOp_XDEREF: result = 0; break;
case SYMS_DwOp_ABS: result = 0; break;
case SYMS_DwOp_AND: result = 0; break;
case SYMS_DwOp_DIV: result = 0; break;
case SYMS_DwOp_MINUS: result = 0; break;
case SYMS_DwOp_MOD: result = 0; break;
case SYMS_DwOp_MUL: result = 0; break;
case SYMS_DwOp_NEG: result = 0; break;
case SYMS_DwOp_NOT: result = 0; break;
case SYMS_DwOp_OR: result = 0; break;
case SYMS_DwOp_PLUS: result = 0; break;
case SYMS_DwOp_PLUS_UCONST: result = 1; break;
case SYMS_DwOp_SHL: result = 0; break;
case SYMS_DwOp_SHR: result = 0; break;
case SYMS_DwOp_SHRA: result = 0; break;
case SYMS_DwOp_XOR: result = 0; break;
case SYMS_DwOp_SKIP: result = 1; break;
case SYMS_DwOp_BRA: result = 1; break;
case SYMS_DwOp_EQ: result = 0; break;
case SYMS_DwOp_GE: result = 0; break;
case SYMS_DwOp_GT: result = 0; break;
case SYMS_DwOp_LE: result = 0; break;
case SYMS_DwOp_LT: result = 0; break;
case SYMS_DwOp_NE: result = 0; break;
case SYMS_DwOp_LIT0: result = 0; break;
case SYMS_DwOp_LIT1: result = 0; break;
case SYMS_DwOp_LIT2: result = 0; break;
case SYMS_DwOp_LIT3: result = 0; break;
case SYMS_DwOp_LIT4: result = 0; break;
case SYMS_DwOp_LIT5: result = 0; break;
case SYMS_DwOp_LIT6: result = 0; break;
case SYMS_DwOp_LIT7: result = 0; break;
case SYMS_DwOp_LIT8: result = 0; break;
case SYMS_DwOp_LIT9: result = 0; break;
case SYMS_DwOp_LIT10: result = 0; break;
case SYMS_DwOp_LIT11: result = 0; break;
case SYMS_DwOp_LIT12: result = 0; break;
case SYMS_DwOp_LIT13: result = 0; break;
case SYMS_DwOp_LIT14: result = 0; break;
case SYMS_DwOp_LIT15: result = 0; break;
case SYMS_DwOp_LIT16: result = 0; break;
case SYMS_DwOp_LIT17: result = 0; break;
case SYMS_DwOp_LIT18: result = 0; break;
case SYMS_DwOp_LIT19: result = 0; break;
case SYMS_DwOp_LIT20: result = 0; break;
case SYMS_DwOp_LIT21: result = 0; break;
case SYMS_DwOp_LIT22: result = 0; break;
case SYMS_DwOp_LIT23: result = 0; break;
case SYMS_DwOp_LIT24: result = 0; break;
case SYMS_DwOp_LIT25: result = 0; break;
case SYMS_DwOp_LIT26: result = 0; break;
case SYMS_DwOp_LIT27: result = 0; break;
case SYMS_DwOp_LIT28: result = 0; break;
case SYMS_DwOp_LIT29: result = 0; break;
case SYMS_DwOp_LIT30: result = 0; break;
case SYMS_DwOp_LIT31: result = 0; break;
case SYMS_DwOp_REG0: result = 0; break;
case SYMS_DwOp_REG1: result = 0; break;
case SYMS_DwOp_REG2: result = 0; break;
case SYMS_DwOp_REG3: result = 0; break;
case SYMS_DwOp_REG4: result = 0; break;
case SYMS_DwOp_REG5: result = 0; break;
case SYMS_DwOp_REG6: result = 0; break;
case SYMS_DwOp_REG7: result = 0; break;
case SYMS_DwOp_REG8: result = 0; break;
case SYMS_DwOp_REG9: result = 0; break;
case SYMS_DwOp_REG10: result = 0; break;
case SYMS_DwOp_REG11: result = 0; break;
case SYMS_DwOp_REG12: result = 0; break;
case SYMS_DwOp_REG13: result = 0; break;
case SYMS_DwOp_REG14: result = 0; break;
case SYMS_DwOp_REG15: result = 0; break;
case SYMS_DwOp_REG16: result = 0; break;
case SYMS_DwOp_REG17: result = 0; break;
case SYMS_DwOp_REG18: result = 0; break;
case SYMS_DwOp_REG19: result = 0; break;
case SYMS_DwOp_REG20: result = 0; break;
case SYMS_DwOp_REG21: result = 0; break;
case SYMS_DwOp_REG22: result = 0; break;
case SYMS_DwOp_REG23: result = 0; break;
case SYMS_DwOp_REG24: result = 0; break;
case SYMS_DwOp_REG25: result = 0; break;
case SYMS_DwOp_REG26: result = 0; break;
case SYMS_DwOp_REG27: result = 0; break;
case SYMS_DwOp_REG28: result = 0; break;
case SYMS_DwOp_REG29: result = 0; break;
case SYMS_DwOp_REG30: result = 0; break;
case SYMS_DwOp_REG31: result = 0; break;
case SYMS_DwOp_BREG0: result = 0; break;
case SYMS_DwOp_BREG1: result = 0; break;
case SYMS_DwOp_BREG2: result = 0; break;
case SYMS_DwOp_BREG3: result = 0; break;
case SYMS_DwOp_BREG4: result = 0; break;
case SYMS_DwOp_BREG5: result = 0; break;
case SYMS_DwOp_BREG6: result = 0; break;
case SYMS_DwOp_BREG7: result = 0; break;
case SYMS_DwOp_BREG8: result = 0; break;
case SYMS_DwOp_BREG9: result = 0; break;
case SYMS_DwOp_BREG10: result = 0; break;
case SYMS_DwOp_BREG11: result = 0; break;
case SYMS_DwOp_BREG12: result = 0; break;
case SYMS_DwOp_BREG13: result = 0; break;
case SYMS_DwOp_BREG14: result = 0; break;
case SYMS_DwOp_BREG15: result = 0; break;
case SYMS_DwOp_BREG16: result = 0; break;
case SYMS_DwOp_BREG17: result = 0; break;
case SYMS_DwOp_BREG18: result = 0; break;
case SYMS_DwOp_BREG19: result = 0; break;
case SYMS_DwOp_BREG20: result = 0; break;
case SYMS_DwOp_BREG21: result = 0; break;
case SYMS_DwOp_BREG22: result = 0; break;
case SYMS_DwOp_BREG23: result = 0; break;
case SYMS_DwOp_BREG24: result = 0; break;
case SYMS_DwOp_BREG25: result = 0; break;
case SYMS_DwOp_BREG26: result = 0; break;
case SYMS_DwOp_BREG27: result = 0; break;
case SYMS_DwOp_BREG28: result = 0; break;
case SYMS_DwOp_BREG29: result = 0; break;
case SYMS_DwOp_BREG30: result = 0; break;
case SYMS_DwOp_BREG31: result = 0; break;
case SYMS_DwOp_REGX: result = 1; break;
case SYMS_DwOp_FBREG: result = 1; break;
case SYMS_DwOp_BREGX: result = 2; break;
case SYMS_DwOp_PIECE: result = 1; break;
case SYMS_DwOp_DEREF_SIZE: result = 1; break;
case SYMS_DwOp_XDEREF_SIZE: result = 1; break;
case SYMS_DwOp_NOP: result = 0; break;
case SYMS_DwOp_PUSH_OBJECT_ADDRESS: result = 0; break;
case SYMS_DwOp_CALL2: result = 1; break;
case SYMS_DwOp_CALL4: result = 1; break;
case SYMS_DwOp_CALL_REF: result = 1; break;
case SYMS_DwOp_FORM_TLS_ADDRESS: result = 0; break;
case SYMS_DwOp_CALL_FRAME_CFA: result = 0; break;
case SYMS_DwOp_BIT_PIECE: result = 2; break;
case SYMS_DwOp_IMPLICIT_VALUE: result = 2; break;
case SYMS_DwOp_STACK_VALUE: result = 0; break;
case SYMS_DwOp_IMPLICIT_POINTER: result = 2; break;
case SYMS_DwOp_ADDRX: result = 1; break;
case SYMS_DwOp_CONSTX: result = 1; break;
case SYMS_DwOp_ENTRY_VALUE: result = 2; break;
case SYMS_DwOp_CONST_TYPE: result = 3; break;
case SYMS_DwOp_REGVAL_TYPE: result = 2; break;
case SYMS_DwOp_DEREF_TYPE: result = 2; break;
case SYMS_DwOp_XDEREF_TYPE: result = 2; break;
case SYMS_DwOp_CONVERT: result = 1; break;
case SYMS_DwOp_REINTERPRET: result = 1; break;
case SYMS_DwOp_GNU_PUSH_TLS_ADDRESS: result = 1; break;
case SYMS_DwOp_GNU_UNINIT: result = 1; break;
}
return(result);
}

//~ generated from code at syms/metaprogram/syms_metaprogram_serial.c:1607
#endif
