[CoreRedirects]

; renaming decorator to trait
+StructRedirects=(OldName="RigVMDecorator", NewName="/Script/RigVM.RigVMTrait")
+PropertyRedirects=(OldName="RigVMNode.DecoratorRootPinNames",NewName="RigVMNode.TraitRootPinNames")

; graph function layout renaming
+StructRedirects=(OldName="RigVMGraphFunctionLayout", NewName="/Script/RigVM.RigVMNodeLayout")
+StructRedirects=(OldName="RigVMGraphFunctionCategory", NewName="/Script/RigVM.RigVMPinCategory")