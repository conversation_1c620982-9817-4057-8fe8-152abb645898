// Microsoft Visual C++ generated resource script.
//
#include "resource.h"

#define APSTUDIO_READONLY_SYMBOLS
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 2 resource.
//
#include <windows.h>

/////////////////////////////////////////////////////////////////////////////
#undef APSTUDIO_READONLY_SYMBOLS

/////////////////////////////////////////////////////////////////////////////
// English (United States) resources

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENU)
LANGUAGE LANG_ENGLISH, SUBLANG_ENGLISH_US
#pragma code_page(1252)

/////////////////////////////////////////////////////////////////////////////
//
// Dialog
//

IDD_OK DIALOGEX 0, 0, 200, 80
STYLE DS_ABSALIGN | DS_SYSMODAL | DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | DS_CENTER | WS_POPUP | WS_CLIPCHILDREN | WS_CAPTION | WS_SYSMENU | WS_THICKFRAME
CAPTION "Dialog"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    EDITTEXT        IDC_MESSAGE,15,15,170,20,ES_MULTILINE | ES_AUTOVSCROLL | ES_AUTOHSCROLL | ES_READONLY | NOT WS_BORDER | WS_VSCROLL | WS_HSCROLL | 0x200
    DEFPUSHBUTTON   "OK",IDC_OK,150,50,50,14
END

IDD_YESNO DIALOGEX 0, 0, 200, 80
STYLE DS_ABSALIGN | DS_SYSMODAL | DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | DS_CENTER | WS_POPUP | WS_CLIPCHILDREN | WS_CAPTION | WS_SYSMENU | WS_THICKFRAME
CAPTION "Dialog"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    EDITTEXT        IDC_MESSAGE,15,15,170,20,ES_MULTILINE | ES_AUTOVSCROLL | ES_AUTOHSCROLL | ES_READONLY | NOT WS_BORDER | WS_VSCROLL | WS_HSCROLL | 0x200
    DEFPUSHBUTTON   "Yes",IDC_YES,100,50,50,14
    PUSHBUTTON      "No",IDC_NO_B,150,50,50,14
END

IDD_OKCANCEL DIALOGEX 0, 0, 200, 80
STYLE DS_ABSALIGN | DS_SYSMODAL | DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | DS_CENTER | WS_POPUP | WS_CLIPCHILDREN | WS_CAPTION | WS_SYSMENU | WS_THICKFRAME
CAPTION "Dialog"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    EDITTEXT        IDC_MESSAGE,15,15,170,20,ES_MULTILINE | ES_AUTOVSCROLL | ES_AUTOHSCROLL | ES_READONLY | NOT WS_BORDER | WS_VSCROLL | WS_HSCROLL | 0x200
    DEFPUSHBUTTON   "OK",IDC_OK,100,50,50,14
    PUSHBUTTON      "Cancel",IDC_CANCEL,150,50,50,14
END

IDD_YESNOCANCEL DIALOGEX 0, 0, 275, 80
STYLE DS_ABSALIGN | DS_SYSMODAL | DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | DS_CENTER | WS_POPUP | WS_CLIPCHILDREN | WS_CAPTION | WS_SYSMENU | WS_THICKFRAME
CAPTION "Dialog"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    EDITTEXT        IDC_MESSAGE,15,15,245,20,ES_MULTILINE | ES_AUTOVSCROLL | ES_AUTOHSCROLL | ES_READONLY | NOT WS_BORDER | WS_VSCROLL | WS_HSCROLL | 0x200
    DEFPUSHBUTTON   "Yes",IDC_YES,100,50,50,14
    PUSHBUTTON      "No",IDC_NO_B,150,50,50,14
    PUSHBUTTON      "Cancel",IDC_CANCEL,200,50,50,14
END

IDD_CANCELRETRYCONTINUE DIALOGEX 0, 0, 275, 80
STYLE DS_ABSALIGN | DS_SYSMODAL | DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | DS_CENTER | WS_POPUP | WS_CLIPCHILDREN | WS_CAPTION | WS_SYSMENU
CAPTION "Dialog"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    EDITTEXT        IDC_MESSAGE,15,15,245,20,ES_MULTILINE | ES_AUTOVSCROLL | ES_AUTOHSCROLL | ES_READONLY | NOT WS_BORDER | WS_VSCROLL | WS_HSCROLL | 0x200
    PUSHBUTTON      "Cancel",IDC_CANCEL,100,50,50,14
    DEFPUSHBUTTON   "Retry",IDC_RETRY,150,50,50,14
    PUSHBUTTON      "Continue",IDC_CONTINUE,200,50,50,14
END

IDD_YESNO2ALL DIALOGEX 0, 0, 275, 80
STYLE DS_ABSALIGN | DS_SYSMODAL | DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | DS_CENTER | WS_POPUP | WS_CLIPCHILDREN | WS_CAPTION | WS_SYSMENU | WS_THICKFRAME
CAPTION "Dialog"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    EDITTEXT        IDC_MESSAGE,15,15,245,20,ES_MULTILINE | ES_AUTOVSCROLL | ES_AUTOHSCROLL | ES_READONLY | NOT WS_BORDER | WS_VSCROLL | WS_HSCROLL | 0x200
    DEFPUSHBUTTON   "Yes",IDC_YES,50,50,50,14
    PUSHBUTTON      "No",IDC_NO_B,100,50,50,14
    PUSHBUTTON      "Yes to all",IDC_YESTOALL,150,50,50,14
    PUSHBUTTON      "No to all",IDC_NOTOALL,200,50,50,14
END

IDD_YESNO2ALLCANCEL DIALOGEX 0, 0, 300, 80
STYLE DS_ABSALIGN | DS_SYSMODAL | DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | DS_CENTER | WS_POPUP | WS_CLIPCHILDREN | WS_CAPTION | WS_SYSMENU | WS_THICKFRAME
CAPTION "Dialog"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    EDITTEXT        IDC_MESSAGE,15,15,270,20,ES_MULTILINE | ES_AUTOVSCROLL | ES_AUTOHSCROLL | ES_READONLY | NOT WS_BORDER | WS_VSCROLL | WS_HSCROLL | 0x200
    PUSHBUTTON      "Yes",IDC_YES,0,50,50,14
    PUSHBUTTON      "Yes to all",IDC_YESTOALL,50,50,50,14
    PUSHBUTTON      "No",IDC_NO_B,100,50,50,14
    PUSHBUTTON      "No to all",IDC_NOTOALL,150,50,50,14
    PUSHBUTTON      "Cancel",IDC_CANCEL,200,50,50,14
END

IDD_YESNOYESTOALL DIALOGEX 0, 0, 275, 80
STYLE DS_ABSALIGN | DS_SYSMODAL | DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | DS_CENTER | WS_POPUP | WS_CLIPCHILDREN | WS_CAPTION | WS_SYSMENU | WS_THICKFRAME
CAPTION "Dialog"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    EDITTEXT        IDC_MESSAGE,15,15,245,20,ES_MULTILINE | ES_AUTOVSCROLL | ES_AUTOHSCROLL | ES_READONLY | NOT WS_BORDER | WS_VSCROLL | WS_HSCROLL | 0x200
    PUSHBUTTON      "Yes",IDC_YES,100,50,50,14
    PUSHBUTTON      "No",IDC_NO_B,150,50,50,14
    PUSHBUTTON      "Yes to all",IDC_YESTOALL,200,50,50,14
END


/////////////////////////////////////////////////////////////////////////////
//
// Accelerator
//

IDR_ACCEL1 ACCELERATORS
BEGIN
    "Y",            IDC_YES,                VIRTKEY 
    "N",            IDC_NO_B,               VIRTKEY 
    VK_ESCAPE,      IDC_CANCEL,             VIRTKEY 
    "C",            IDC_COPY,               VIRTKEY, CONTROL
END


/////////////////////////////////////////////////////////////////////////////
//
// DESIGNINFO
//

#ifdef APSTUDIO_INVOKED
GUIDELINES DESIGNINFO
BEGIN
    IDD_OK, DIALOG
    BEGIN
    END

    IDD_CANCELRETRYCONTINUE, DIALOG
    BEGIN
    END

    IDD_YESNO2ALL, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 268
        TOPMARGIN, 7
        BOTTOMMARGIN, 111
    END

    IDD_YESNO2ALLCANCEL, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 290
        TOPMARGIN, 7
        BOTTOMMARGIN, 119
    END

    IDD_YESNOYESTOALL, DIALOG
    BEGIN
    END
END
#endif    // APSTUDIO_INVOKED


#ifdef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// TEXTINCLUDE
//

1 TEXTINCLUDE 
BEGIN
    "resource.h\0"
END

2 TEXTINCLUDE 
BEGIN
    "#include <windows.h>\r\n"
    "\0"
END

#endif    // APSTUDIO_INVOKED


/////////////////////////////////////////////////////////////////////////////
//
// AFX_DIALOG_LAYOUT
//

IDD_CANCELRETRYCONTINUE AFX_DIALOG_LAYOUT
BEGIN
    0
END

#endif    // English (United States) resources
/////////////////////////////////////////////////////////////////////////////



#ifndef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 3 resource.
//


/////////////////////////////////////////////////////////////////////////////
#endif    // not APSTUDIO_INVOKED

