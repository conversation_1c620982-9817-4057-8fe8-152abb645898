<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>LPV code from Microsoft</Name>
  <Location>/Engine/Source/Runtime/Core/Private/HAL/LowLevelMemoryMap.h</Location>
  <Date>2016-06-10T17:06:31.4977906-04:00</Date>
  <Function>Implements a dictionary data structure without using Un<PERSON>'s memory manager. Used in our low-level memory tracker system.</Function>
  <Justification />
  <Eula>Custom agreement between Epic/Microsoft which allows code sharing</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>None</LicenseFolder>
</TpsData>