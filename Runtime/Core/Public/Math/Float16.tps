<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>float to half variants</Name>
  <Location>/Engine/Source/Runtime/Core/Public/Math/Float16.h</Location>
  <Function>Handles denormalized values (the smallest values the format can express) whenever converting a 32 bit floating point to a 16 bit floating point.</Function>
  <Eula>https://gist.github.com/rygorous/2156668#file-gistfile1-cpp-L215-L221</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>None</LicenseFolder>
</TpsData>
