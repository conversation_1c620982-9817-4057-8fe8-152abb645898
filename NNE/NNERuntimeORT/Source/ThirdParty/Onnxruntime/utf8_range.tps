<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>utf8_range</Name>
  <!-- Software Name and Version  -->
<!-- Software Name: utf8_range
    Download Link: https://github.com/protocolbuffers/utf8_range/archive/72c943dea2b9240cd09efde15191e144bc7c7d38.zip
    Version: 
    Note: Dependecy of ONNX Runtime
    -->
<Location>/Engine/Plugins/NNE/NNERuntimeORT/Source/ThirdParty/Onnxruntime/</Location>
<Function>The software is part of the Onnxruntime library, which is used to run neural network inference through the onnx runtime backend and also to optimize ML models.</Function>
<Eula>https://github.com/protocolbuffers/utf8_range/blob/72c943dea2b9240cd09efde15191e144bc7c7d38/LICENSE</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensee</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses</LicenseFolder>
</TpsData>
 


 