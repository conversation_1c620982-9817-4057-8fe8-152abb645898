<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>onnx</Name>
  <!-- Software Name and Version  -->
<!-- Software Name: onnx
    Download Link: https://github.com/onnx/onnx/archive/refs/tags/v1.15.0.zip
    Version: 1.15.0
    Note: Dependecy of ONNX Runtime
    -->
<Location>/Engine/Plugins/NNE/NNERuntimeORT/Source/ThirdParty/Onnxruntime/</Location>
<Function>The software is part of the Onnxruntime library, which is used to run neural network inference through the onnx runtime backend and also to optimize ML models.</Function>
<Eula>https://github.com/onnx/onnx/blob/v1.15.0/LICENSE</Eula>
  <RedistributeTo>
    <EndUserGroup>Licencees</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses</LicenseFolder>
</TpsData>
 


 