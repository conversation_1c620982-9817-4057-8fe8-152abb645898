#include "NS_ThrowActor.h"
#include "GameFramework/ProjectileMovementComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Components/BoxComponent.h"
#include "Zombie/NS_ZombieBase.h"
#include "Kismet/GameplayStatics.h"
#include "Perception/AISense_Hearing.h"
#include "GeometryCollection/GeometryCollectionActor.h"
#include "GeometryCollection/GeometryCollectionComponent.h"

ANS_ThrowActor::ANS_ThrowActor()
{
	PrimaryActorTick.bCanEverTick = false; 

	// 지오메트리 컬렉션 컴포넌트 (메인)
	BottleGeometry = CreateDefaultSubobject<UGeometryCollectionComponent>(TEXT("BottleGeometry"));
	RootComponent = BottleGeometry;
	BottleGeometry->SetSimulatePhysics(false); // 던지는 동안은 물리 시뮬레이션 끄기
	BottleGeometry->SetCollisionEnabled(ECollisionEnabled::QueryOnly); // 오버랩 감지만
	BottleGeometry->SetCollisionResponseToAllChannels(ECR_Ignore);
	BottleGeometry->SetCollisionResponseToChannel(ECC_WorldStatic, ECR_Overlap);
	BottleGeometry->SetCollisionResponseToChannel(ECC_WorldDynamic, ECR_Overlap);

	// 지오메트리 컬렉션 기본 설정
	BottleGeometry->SetNotifyBreaks(true); // 깨짐 알림 활성화

	// 박스콜리전 오버렙이벤트
	OverlapCollision = CreateDefaultSubobject<UBoxComponent>(TEXT("OverlapCollision"));
	OverlapCollision->SetupAttachment(RootComponent);
	OverlapCollision->SetBoxExtent(FVector(50.f, 50.f, 50.f)); // 크기는 추가로 에디터에서 조정해줘야할듯
	OverlapCollision->SetCollisionProfileName(TEXT("OverlapAll")); // 모든 것에 오버랩하고
	OverlapCollision->SetGenerateOverlapEvents(true); // 오버랩 이벤트 생성 활성화해주고
	OverlapCollision->OnComponentBeginOverlap.AddDynamic(this, &ANS_ThrowActor::OnOverlapBegin);

	//날아가는 프로젝트일컴포넌트 설정
	ProjectileMovement = CreateDefaultSubobject<UProjectileMovementComponent>(TEXT("ProjectileMovement"));
	ProjectileMovement->UpdatedComponent = BottleMesh; // 스테틱메쉬에 위치를 업데이틓고
	ProjectileMovement->InitialSpeed = 1200.f;
	ProjectileMovement->MaxSpeed = 1200.f;
	ProjectileMovement->bRotationFollowsVelocity = true;
	ProjectileMovement->bShouldBounce = false; // 오버렙방식이라 바운드는 적용안하고
	ProjectileMovement->ProjectileGravityScale = 1.0f;

	bReplicates = true;
}

void ANS_ThrowActor::BeginPlay()
{
	Super::BeginPlay();
}

void ANS_ThrowActor::LaunchInDirection(const FVector& Direction)
{
	if (ProjectileMovement)
	{
		ProjectileMovement->Velocity = Direction * ProjectileMovement->InitialSpeed;
		ProjectileMovement->Activate();
	}
}

void ANS_ThrowActor::OnOverlapBegin(
	UPrimitiveComponent* OverlappedComp,
	AActor* OtherActor,
	UPrimitiveComponent* OtherComp,
	int32 OtherBodyIndex,
	bool bFromSweep,
	const FHitResult& SweepResult
)
{
	// 자신 또는 소유자는 무시하고
	if (OtherActor == this || OtherActor == GetOwner())
		return;

	if (!FractureActorClass)
		return;

	// 병깨지는 사운드는 1번만 재생되도록 하고
	if (!bHasPlayedImpactSound)
	{
		if (ImpactSound)
			UGameplayStatics::PlaySoundAtLocation(this, ImpactSound, GetActorLocation());
	}

	// 원본 투사체 제거
	Destroy();
	
	// 현재 오버렙된 위치랑 기존 속도를 가져오고
	FVector SpawnLocation = GetActorLocation();
	FVector CurrentVelocity = ProjectileMovement->Velocity;

	// Z축 밑으로 떨어지는 속도는 0으로 설정해서 액터를 뚫고 아래로 안내려가도록해야함
	CurrentVelocity.Z = FMath::Max(0.f, CurrentVelocity.Z);

	// 충돌 강도 체크 (벽에 무조건 깨지도록)
	float CollisionStrength = CurrentVelocity.Size();
	bool bShouldBreak = CollisionStrength > MinBreakVelocity; // 충분한 속도면 무조건 깨짐

	// 파편 흩어짐은 매우 작게 (그 자리에서 깨지도록)
	FVector FragmentVelocity = CurrentVelocity * GeometrySpeedScale;

	// 모든 경우에 파편 속도를 대폭 줄임 (그 자리에서 깨지도록)
	FragmentVelocity *= 0.1f; // 추가로 10배 감소

	// 벽 충돌인지 확인 (수직 충돌이면 파편을 더 적게 흩어뜨림)
	FVector HitNormal = FVector::ZeroVector;
	if (OtherComp)
	{
		// 충돌 지점에서 법선 벡터 계산
		FVector ToHit = (GetActorLocation() - OtherActor->GetActorLocation()).GetSafeNormal();
		HitNormal = ToHit;

		// 벽 충돌이면 파편 속도를 더 줄임
		float DotProduct = FVector::DotProduct(CurrentVelocity.GetSafeNormal(), HitNormal);
		if (FMath::Abs(DotProduct) > 0.7f) // 거의 수직 충돌
		{
			FragmentVelocity *= WallCollisionDamping; // 벽 충돌시 파편을 더 적게 흩어뜨림
			UE_LOG(LogTemp, Warning, TEXT("벽 충돌 감지 - 파편 속도 추가 감소 (감쇠: %f)"), WallCollisionDamping);
		}
	}

	// 최종적으로 매우 작은 속도로 제한 (에디터에서 조정 가능)
	FragmentVelocity = FragmentVelocity.GetClampedToMaxSize(MaxFragmentSpeed);

	UE_LOG(LogTemp, Warning, TEXT("병 깨짐: 충돌강도=%f, 깨짐여부=%s, 파편속도=%s"),
		CollisionStrength, bShouldBreak ? TEXT("TRUE") : TEXT("FALSE"), *FragmentVelocity.ToString());

	// 지오메트레컬렉션 액터 스폰
	FActorSpawnParameters SpawnParams;
	SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;

	// 충돌 강도가 충분하지 않으면 깨지지 않음
	if (!bShouldBreak)
	{
		UE_LOG(LogTemp, Warning, TEXT("충돌 강도 부족 - 병이 깨지지 않음"));
		return;
	}

	// 지오메트리 생성은 오버렙이벤트가 된 위치에서 생성해주고
	AGeometryCollectionActor* FractureActor = GetWorld()->SpawnActor<AGeometryCollectionActor>(
		FractureActorClass,
		SpawnLocation,
		FragmentVelocity.Rotation(),
		SpawnParams
	);

	if (FractureActor)
	{
		// 자연스러운 파편 흩어짐 적용
		UGeometryCollectionComponent* GeoComp = FractureActor->GetGeometryCollectionComponent();
		if (GeoComp)
		{
			GeoComp->SetSimulatePhysics(true);
			float Mass = GeoComp->GetMass();

			// 던지는 방향을 기준으로 파편 흩어뜨리기
			FVector ThrowDirection = CurrentVelocity.GetSafeNormal(); // 던지는 방향
			FVector RightVector = FVector::CrossProduct(ThrowDirection, FVector::UpVector).GetSafeNormal();
			FVector UpVector = FVector::CrossProduct(RightVector, ThrowDirection).GetSafeNormal();

			// 던지는 방향 기준으로 랜덤 산란 계산 (에디터에서 조정 가능)
			FVector DirectionalScatter =
				ThrowDirection * FMath::RandRange(0.f, HorizontalScatterRange * ForwardScatterRatio) +  // 앞쪽으로
				RightVector * FMath::RandRange(-HorizontalScatterRange * SideScatterRatio, HorizontalScatterRange * SideScatterRatio) + // 좌우로
				UpVector * FMath::RandRange(VerticalScatterMin, VerticalScatterMax); // 위아래로

			FVector FinalVelocity = FragmentVelocity + DirectionalScatter;

			UE_LOG(LogTemp, Warning, TEXT("던지는 방향: %s, 파편 방향: %s"),
				*ThrowDirection.ToString(), *FinalVelocity.GetSafeNormal().ToString());
			GeoComp->AddImpulse(FinalVelocity * Mass);

			UE_LOG(LogTemp, Warning, TEXT("지오메트리 컬렉션 임펄스 적용: %s (Mass: %f, 랜덤산란: %s)"),
				*FinalVelocity.ToString(), Mass, *RandomScatter.ToString());
		}

		// 스폰타임은 5초로 설정
		FractureActor->SetLifeSpan(5.f);
	}
}

void ANS_ThrowActor::ActivateGeometryBreaking(const FVector& ImpactVelocity)
{
	if (!BottleGeometry)
	{
		UE_LOG(LogTemp, Error, TEXT("BottleGeometry가 null입니다!"));
		return;
	}

	UE_LOG(LogTemp, Warning, TEXT("지오메트리 컬렉션 깨짐 활성화 - 충돌 속도: %s"), *ImpactVelocity.ToString());

	// 물리 시뮬레이션 활성화
	BottleGeometry->SetSimulatePhysics(true);

	// 깨짐 효과를 위한 임펄스 적용
	float CollisionStrength = ImpactVelocity.Size();

	// 던지는 방향을 기준으로 파편 흩어뜨리기
	FVector ThrowDirection = ImpactVelocity.GetSafeNormal();
	FVector RightVector = FVector::CrossProduct(ThrowDirection, FVector::UpVector).GetSafeNormal();
	FVector UpVector = FVector::CrossProduct(RightVector, ThrowDirection).GetSafeNormal();

	// 방향성 파편 속도 계산
	FVector FragmentVelocity = ImpactVelocity * GeometrySpeedScale * 0.1f; // 매우 작게
	FragmentVelocity = FragmentVelocity.GetClampedToMaxSize(MaxFragmentSpeed);

	// 방향성 산란 추가
	FVector DirectionalScatter =
		ThrowDirection * FMath::RandRange(0.f, HorizontalScatterRange * ForwardScatterRatio) +
		RightVector * FMath::RandRange(-HorizontalScatterRange * SideScatterRatio, HorizontalScatterRange * SideScatterRatio) +
		UpVector * FMath::RandRange(VerticalScatterMin, VerticalScatterMax);

	FVector FinalVelocity = FragmentVelocity + DirectionalScatter;

	// 임펄스 적용
	float Mass = BottleGeometry->GetMass();
	BottleGeometry->AddImpulse(FinalVelocity * Mass);

	UE_LOG(LogTemp, Warning, TEXT("지오메트리 컬렉션 임펄스 적용: %s (Mass: %f)"),
		*FinalVelocity.ToString(), Mass);

	// 일정 시간 후 액터 제거
	SetLifeSpan(10.0f);
}