#include "NS_ThrowActor.h"
#include "GameFramework/ProjectileMovementComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Components/BoxComponent.h"
#include "Zombie/NS_ZombieBase.h"
#include "Kismet/GameplayStatics.h"
#include "Perception/AISense_Hearing.h"
#include "GeometryCollection/GeometryCollectionActor.h"
#include "GeometryCollection/GeometryCollectionComponent.h"

ANS_ThrowActor::ANS_ThrowActor()
{
	PrimaryActorTick.bCanEverTick = false; 

	// 지오메트리 컬렉션 컴포넌트 (메인)
	BottleGeometry = CreateDefaultSubobject<UGeometryCollectionComponent>(TEXT("BottleGeometry"));
	RootComponent = BottleGeometry;

	// 던지는 동안 Kinematic으로 설정 (ProjectileMovement가 제어)
	BottleGeometry->SetSimulatePhysics(false);
	BottleGeometry->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
	BottleGeometry->SetCollisionObjectType(ECC_WorldDynamic);
	BottleGeometry->SetCollisionResponseToAllChannels(ECR_Block); // 벽과 충돌
	BottleGeometry->SetCollisionResponseToChannel(ECC_Pawn, ECR_Overlap); // 플레이어와는 오버랩

	// 지오메트리 컬렉션 기본 설정
	BottleGeometry->SetNotifyBreaks(true); // 깨짐 알림 활성화
	BottleGeometry->SetMobility(EComponentMobility::Movable); // 이동 가능하게 설정

	// 박스콜리전 오버렙이벤트
	OverlapCollision = CreateDefaultSubobject<UBoxComponent>(TEXT("OverlapCollision"));
	OverlapCollision->SetupAttachment(RootComponent);
	OverlapCollision->SetBoxExtent(FVector(50.f, 50.f, 50.f)); // 크기는 추가로 에디터에서 조정해줘야할듯
	OverlapCollision->SetCollisionProfileName(TEXT("OverlapAll")); // 모든 것에 오버랩하고
	OverlapCollision->SetGenerateOverlapEvents(true); // 오버랩 이벤트 생성 활성화해주고
	OverlapCollision->OnComponentBeginOverlap.AddDynamic(this, &ANS_ThrowActor::OnOverlapBegin);

	// 날아가는 프로젝타일 컴포넌트 설정
	ProjectileMovement = CreateDefaultSubobject<UProjectileMovementComponent>(TEXT("ProjectileMovement"));
	ProjectileMovement->UpdatedComponent = BottleGeometry; // 지오메트리 컬렉션에 위치 업데이트
	ProjectileMovement->InitialSpeed = 1200.f;
	ProjectileMovement->MaxSpeed = 1200.f;
	ProjectileMovement->bRotationFollowsVelocity = true;
	ProjectileMovement->bShouldBounce = false; // 오버랩 방식이라 바운드는 적용 안함
	ProjectileMovement->ProjectileGravityScale = 1.0f;

	bReplicates = true;
}

void ANS_ThrowActor::BeginPlay()
{
	Super::BeginPlay();
}

void ANS_ThrowActor::LaunchInDirection(const FVector& Direction)
{
	if (ProjectileMovement)
	{
		ProjectileMovement->Velocity = Direction * ProjectileMovement->InitialSpeed;
		ProjectileMovement->Activate();
	}
}

void ANS_ThrowActor::OnOverlapBegin(
	UPrimitiveComponent* OverlappedComp,
	AActor* OtherActor,
	UPrimitiveComponent* OtherComp,
	int32 OtherBodyIndex,
	bool bFromSweep,
	const FHitResult& SweepResult
)
{
	// 자기 자신이나 던진 플레이어와의 충돌은 무시
	if (OtherActor == this || OtherActor == GetOwner())
	{
		return;
	}

	// 좀비 데미지 제거

	// 사운드 재생 (한 번만)
	if (ImpactSound && !bHasPlayedImpactSound)
	{
		UGameplayStatics::PlaySoundAtLocation(this, ImpactSound, GetActorLocation());
		bHasPlayedImpactSound = true;
		UE_LOG(LogTemp, Warning, TEXT("충돌 사운드 재생"));
	}

	// AI 청각 자극 생성
	UAISense_Hearing::ReportNoiseEvent(GetWorld(), GetActorLocation(), 1.0f, this, 1000.0f);
	
	// 현재 속도 가져오기
	FVector CurrentVelocity = ProjectileMovement->Velocity;
	float CollisionStrength = CurrentVelocity.Size();

	// 충돌 강도가 충분하지 않으면 깨지지 않음
	if (CollisionStrength < MinBreakVelocity)
	{
		UE_LOG(LogTemp, Warning, TEXT("충돌 강도 부족 - 병이 깨지지 않음 (%f < %f)"), CollisionStrength, MinBreakVelocity);
		return;
	}

	UE_LOG(LogTemp, Warning, TEXT("병 깨짐 시작 - 충돌강도: %f"), CollisionStrength);

	// 기존 지오메트리 컬렉션 활성화 (새로 스폰하지 않음!)
	ActivateGeometryBreaking(CurrentVelocity);

	// Destroy()는 ActivateGeometryBreaking에서 SetLifeSpan으로 처리
}

void ANS_ThrowActor::ActivateGeometryBreaking(const FVector& ImpactVelocity)
{
	if (!BottleGeometry)
	{
		UE_LOG(LogTemp, Error, TEXT("BottleGeometry가 null입니다!"));
		return;
	}

	UE_LOG(LogTemp, Warning, TEXT("지오메트리 컬렉션 깨짐 활성화 - 충돌 속도: %s"), *ImpactVelocity.ToString());

	// 물리 시뮬레이션 활성화
	BottleGeometry->SetSimulatePhysics(true);

	// 깨짐 효과를 위한 임펄스 적용
	float CollisionStrength = ImpactVelocity.Size();

	// 던지는 방향을 기준으로 파편 흩어뜨리기
	FVector ThrowDirection = ImpactVelocity.GetSafeNormal();
	FVector RightVector = FVector::CrossProduct(ThrowDirection, FVector::UpVector).GetSafeNormal();
	FVector UpVector = FVector::CrossProduct(RightVector, ThrowDirection).GetSafeNormal();

	// 방향성 파편 속도 계산
	FVector FragmentVelocity = ImpactVelocity * GeometrySpeedScale * 0.1f; // 매우 작게
	FragmentVelocity = FragmentVelocity.GetClampedToMaxSize(MaxFragmentSpeed);

	// 방향성 산란 추가
	FVector DirectionalScatter =
		ThrowDirection * FMath::RandRange(0.f, HorizontalScatterRange * ForwardScatterRatio) +
		RightVector * FMath::RandRange(-HorizontalScatterRange * SideScatterRatio, HorizontalScatterRange * SideScatterRatio) +
		UpVector * FMath::RandRange(VerticalScatterMin, VerticalScatterMax);

	FVector FinalVelocity = FragmentVelocity + DirectionalScatter;

	// 임펄스 적용
	float Mass = BottleGeometry->GetMass();
	BottleGeometry->AddImpulse(FinalVelocity * Mass);

	UE_LOG(LogTemp, Warning, TEXT("지오메트리 컬렉션 임펄스 적용: %s (Mass: %f)"),
		*FinalVelocity.ToString(), Mass);

	// 일정 시간 후 액터 제거
	SetLifeSpan(10.0f);
}