#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "GeometryCollection/GeometryCollectionActor.h"
#include "NS_ThrowActor.generated.h"

class UStaticMeshComponent;
class UProjectileMovementComponent;
class UGeometryCollectionComponent;
class UBoxComponent; // 추가: BoxCollisionComponent를 사용하기 위함

UCLASS()
class TEAMLUNATIC_NOSIGNAL_API ANS_ThrowActor : public AActor
{
	GENERATED_BODY()

	ANS_ThrowActor();
public:

	// 날아갈 병 메쉬
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Components")
	UStaticMeshComponent* BottleMesh;

	// 추가: 오버랩 감지를 위한 박스 콜리전
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Components")
	UBoxComponent* OverlapCollision;

	// 날아갈 궤적 컴포넌트
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	UProjectileMovementComponent* ProjectileMovement;

	// 땅이나 어디든 부딪쳤을때 부서질 지오메트리
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Throw")
	TSubclassOf<AGeometryCollectionActor> FractureActorClass;

	// Hit오버렙 이벤트에서 출력될 사운드 변수
	UPROPERTY(EditAnywhere, Category = "Sound")
	USoundBase* ImpactSound;

	// 지오메트리 컬렉션 속도 배율 (에디터에서 조정 가능)
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Throw", meta = (ClampMin = "0.01", ClampMax = "1.0"))
	float GeometrySpeedScale = 0.025f; // 원래 값으로 복원 (자연스러운 파편)

	// 깨짐 판정을 위한 최소 충돌 강도
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Throw", meta = (ClampMin = "100.0", ClampMax = "1000.0"))
	float MinBreakVelocity = 300.f;

	// 벽 충돌시 파편 속도 감소 배율
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Throw", meta = (ClampMin = "0.1", ClampMax = "1.0"))
	float WallCollisionDamping = 0.3f;

	// 파편 랜덤 산란 범위 설정 (그 자리에서 깨지도록 작게)
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fragment Scatter")
	float HorizontalScatterRange = 30.f; // 좌우/앞뒤 흩어짐 범위 (작게)

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fragment Scatter")
	float VerticalScatterMin = -10.f; // 수직 흩어짐 최소값 (아래로)

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fragment Scatter")
	float VerticalScatterMax = 20.f; // 수직 흩어짐 최대값 (위로)

	// 파편 최대 속도 제한
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fragment Scatter")
	float MaxFragmentSpeed = 50.f; // 파편 최대 속도

	// 방향별 파편 흩어짐 비율
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fragment Scatter", meta = (ClampMin = "0.0", ClampMax = "1.0"))
	float ForwardScatterRatio = 1.0f; // 앞쪽 흩어짐 비율 (던지는 방향)

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fragment Scatter", meta = (ClampMin = "0.0", ClampMax = "1.0"))
	float SideScatterRatio = 0.5f; // 좌우 흩어짐 비율

	// 사운드 재생 여부 변수
	bool bHasPlayedImpactSound = false;

protected:
	virtual void BeginPlay() override;

public:

	void LaunchInDirection(const FVector& Direction);
	
	UFUNCTION()
	void OnOverlapBegin(UPrimitiveComponent* OverlappedComp, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult);

};