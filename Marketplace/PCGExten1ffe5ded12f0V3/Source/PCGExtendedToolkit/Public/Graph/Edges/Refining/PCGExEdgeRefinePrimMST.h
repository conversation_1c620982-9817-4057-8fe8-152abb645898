// Copyright 2025 Timothé Lapetite and contributors
// Released under the MIT license https://opensource.org/license/MIT/

#pragma once

#include "CoreMinimal.h"
#include "PCGExEdgeRefineOperation.h"
#include "Graph/Pathfinding/Heuristics/PCGExHeuristics.h"
#include "Graph/Pathfinding/Search/PCGExScoredQueue.h"
#include "PCGExEdgeRefinePrimMST.generated.h"

/**
 * 
 */
UCLASS(MinimalAPI, BlueprintType, meta=(DisplayName="Refine : MST (Prim)"))
class UPCGExEdgeRefinePrimMST : public UPCGExEdgeRefineOperation
{
	GENERATED_BODY()

public:
	virtual bool GetDefaultEdgeValidity() override { return bInvert; }
	virtual bool RequiresHeuristics() override { return true; }

	virtual void CopySettingsFrom(const UPCGExOperation* Other) override
	{
		Super::CopySettingsFrom(Other);
		if (const UPCGExEdgeRefinePrimMST* TypedOther = Cast<UPCGExEdgeRefinePrimMST>(Other))
		{
			bInvert = TypedOther->bInvert;
		}
	}

	virtual void Process() override
	{
		const int32 NumNodes = Cluster->Nodes->Num();

		TBitArray<> Visited;
		Visited.Init(false, NumNodes);

		const TUniquePtr<PCGExSearch::FScoredQueue> ScoredQueue = MakeUnique<PCGExSearch::FScoredQueue>(NumNodes, RoamingSeedNode->Index, 0);
		const TSharedPtr<PCGEx::FHashLookup> TravelStack = PCGEx::NewHashLookup<PCGEx::FArrayHashLookup>(PCGEx::NH64(-1, -1), NumNodes);

		int32 CurrentNodeIndex;
		double CurrentNodeScore;
		while (ScoredQueue->Dequeue(CurrentNodeIndex, CurrentNodeScore))
		{
			const PCGExCluster::FNode& Current = *Cluster->GetNode(CurrentNodeIndex);
			Visited[CurrentNodeIndex] = true;

			for (const PCGExGraph::FLink Lk : Current.Links)
			{
				const uint32 NeighborIndex = Lk.Node;
				const uint32 EdgeIndex = Lk.Edge;

				if (Visited[NeighborIndex]) { continue; } // Exit early

				const PCGExCluster::FNode& AdjacentNode = *Cluster->GetNode(NeighborIndex);
				PCGExGraph::FEdge& Edge = *Cluster->GetEdge(EdgeIndex);

				const double Score = Heuristics->GetEdgeScore(Current, AdjacentNode, Edge, *RoamingSeedNode, *RoamingGoalNode, nullptr, TravelStack);
				if (!ScoredQueue->Enqueue(NeighborIndex, Score)) { continue; }

				TravelStack->Set(NeighborIndex, PCGEx::NH64(CurrentNodeIndex, EdgeIndex));
			}
		}

		for (int32 i = 0; i < NumNodes; i++)
		{
			int32 Node;
			int32 EdgeIndex;

			PCGEx::NH64(TravelStack->Get(i), Node, EdgeIndex);
			if (Node == -1 || EdgeIndex == -1) { continue; }

			Cluster->GetEdge(EdgeIndex)->bValid = !bInvert;
		}
	}

	/** */
	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = Settings, meta=(PCG_Overridable))
	bool bInvert = false;
};
