// Copyright 2025 Timothé Lapetite and contributors
// Released under the MIT license https://opensource.org/license/MIT/

#include "AssetStaging/PCGExAssetStaging.h"


#define LOCTEXT_NAMESPACE "PCGExAssetStagingElement"
#define PCGEX_NAMESPACE AssetStaging

PCGEX_INITIALIZE_ELEMENT(AssetStaging)

TArray<FPCGPinProperties> UPCGExAssetStagingSettings::InputPinProperties() const
{
	TArray<FPCGPinProperties> PinProperties = Super::InputPinProperties();

	if (CollectionSource == EPCGExCollectionSource::AttributeSet)
	{
		PCGEX_PIN_PARAM(PCGExAssetCollection::SourceAssetCollection, "Attribute set to be used as collection.", Required, {})
	}

	return PinProperties;
}

TArray<FPCGPinProperties> UPCGExAssetStagingSettings::OutputPinProperties() const
{
	TArray<FPCGPinProperties> PinProperties = Super::OutputPinProperties();
	if (OutputMode == EPCGExStagingOutputMode::CollectionMap)
	{
		PCGEX_PIN_PARAM(PCGExStaging::OutputCollectionMapLabel, "Collection map generated by a staging node.", Required, {})
	}
	return PinProperties;
}

bool FPCGExAssetStagingElement::Boot(FPCGExContext* InContext) const
{
	if (!FPCGExPointsProcessorElement::Boot(InContext)) { return false; }

	PCGEX_CONTEXT_AND_SETTINGS(AssetStaging)

	if (Settings->bOutputMaterialPicks)
	{
		PCGEX_VALIDATE_NAME(Settings->MaterialAttributePrefix)
		Context->bPickMaterials = true;
	}

	if (Settings->CollectionSource == EPCGExCollectionSource::Asset)
	{
		Context->MainCollection = PCGExHelpers::LoadBlocking_AnyThread(Settings->AssetCollection);
		if (!Context->MainCollection)
		{
			PCGE_LOG(Error, GraphAndLog, FTEXT("Missing asset collection."));
			return false;
		}
	}
	else
	{
		if (Settings->OutputMode == EPCGExStagingOutputMode::CollectionMap)
		{
			PCGE_LOG(Error, GraphAndLog, FTEXT("Collection Map output is not supported with collections built from attribute sets."));
			return false;
		}

		Context->MainCollection = Settings->AttributeSetDetails.TryBuildCollection(Context, PCGExAssetCollection::SourceAssetCollection, false);
		if (!Context->MainCollection)
		{
			PCGE_LOG(Error, GraphAndLog, FTEXT("Failed to build collection from attribute set."));
			return false;
		}
	}

	if (Context->bPickMaterials && Context->MainCollection->GetType() != PCGExAssetCollection::EType::Mesh)
	{
		Context->bPickMaterials = false;
		PCGE_LOG(Warning, GraphAndLog, FTEXT("Pick Material is set to true, but the selected collection doesn't support material picking."));
	}

	PCGEX_VALIDATE_NAME(Settings->AssetPathAttributeName)

	if (Settings->WeightToAttribute == EPCGExWeightOutputMode::Raw || Settings->WeightToAttribute == EPCGExWeightOutputMode::Normalized)
	{
		PCGEX_VALIDATE_NAME_CONSUMABLE(Settings->WeightAttributeName)
	}

	if (Settings->OutputMode == EPCGExStagingOutputMode::CollectionMap)
	{
		Context->CollectionPickDatasetPacker = MakeShared<PCGExStaging::FPickPacker>(Context);
	}

	return true;
}


void FPCGExAssetStagingContext::RegisterAssetDependencies()
{
	FPCGExPointsProcessorContext::RegisterAssetDependencies();

	PCGEX_SETTINGS_LOCAL(AssetStaging)

	if (Settings->CollectionSource == EPCGExCollectionSource::AttributeSet)
	{
		MainCollection->GetAssetPaths(GetRequiredAssets(), PCGExAssetCollection::ELoadingFlags::Recursive);
	}
	else
	{
		MainCollection->GetAssetPaths(GetRequiredAssets(), PCGExAssetCollection::ELoadingFlags::RecursiveCollectionsOnly);
	}
}


void FPCGExAssetStagingElement::PostLoadAssetsDependencies(FPCGExContext* InContext) const
{
	FPCGExPointsProcessorElement::PostLoadAssetsDependencies(InContext);

	PCGEX_CONTEXT_AND_SETTINGS(AssetStaging)

	if (Settings->CollectionSource == EPCGExCollectionSource::AttributeSet)
	{
		// Internal collection, assets have been loaded at this point
		Context->MainCollection->RebuildStagingData(true);
	}
}

bool FPCGExAssetStagingElement::PostBoot(FPCGExContext* InContext) const
{
	PCGEX_CONTEXT_AND_SETTINGS(AssetStaging)

	Context->MainCollection->LoadCache();

	return FPCGExPointsProcessorElement::PostBoot(InContext);
}

bool FPCGExAssetStagingElement::ExecuteInternal(FPCGContext* InContext) const
{
	TRACE_CPUPROFILER_EVENT_SCOPE(FPCGExAssetStagingElement::Execute);

	PCGEX_CONTEXT_AND_SETTINGS(AssetStaging)
	PCGEX_EXECUTION_CHECK
	PCGEX_ON_INITIAL_EXECUTION
	{
		if (!Context->StartBatchProcessingPoints<PCGExPointsMT::TBatch<PCGExAssetStaging::FProcessor>>(
			[&](const TSharedPtr<PCGExData::FPointIO>& Entry) { return true; },
			[&](const TSharedPtr<PCGExPointsMT::TBatch<PCGExAssetStaging::FProcessor>>& NewBatch)
			{
				NewBatch->bRequiresWriteStep = Settings->bPruneEmptyPoints;
			}))
		{
			return Context->CancelExecution(TEXT("Could not find any points to process."));
		}
	}

	PCGEX_POINTS_BATCH_PROCESSING(PCGEx::State_Done)

	Context->MainPoints->StageOutputs();

	if (Settings->OutputMode == EPCGExStagingOutputMode::CollectionMap)
	{
		UPCGParamData* OutputSet = NewObject<UPCGParamData>();
		Context->CollectionPickDatasetPacker->PackToDataset(OutputSet);

		FPCGTaggedData& OutData = Context->OutputData.TaggedData.Emplace_GetRef();
		OutData.Pin = PCGExStaging::OutputCollectionMapLabel;
		OutData.Data = OutputSet;
	}

	return Context->TryComplete();
}

namespace PCGExAssetStaging
{
	bool FProcessor::Process(const TSharedPtr<PCGExMT::FTaskManager>& InAsyncManager)
	{
		TRACE_CPUPROFILER_EVENT_SCOPE(PCGExAssetStaging::Process);

		// Must be set before process for filters
		PointDataFacade->bSupportsScopedGet = Context->bScopedAttributeGet;

		if (!FPointsProcessor::Process(InAsyncManager)) { return false; }

		PCGEX_INIT_IO(PointDataFacade->Source, PCGExData::EIOInit::Duplicate)

		NumPoints = PointDataFacade->GetNum();

		if (Context->bPickMaterials)
		{
			CachedPicks.Init(nullptr, NumPoints);
			MaterialPick.Init(-1, NumPoints);
		}

		FittingHandler.ScaleToFit = Settings->ScaleToFit;
		FittingHandler.Justification = Settings->Justification;

		if (!FittingHandler.Init(ExecutionContext, PointDataFacade)) { return false; }

		Variations = Settings->Variations;
		Variations.Init(Settings->Seed);


		Helper = MakeUnique<PCGExAssetCollection::TDistributionHelper<UPCGExAssetCollection, FPCGExAssetCollectionEntry>>(Context->MainCollection, Settings->DistributionSettings);
		if (!Helper->Init(ExecutionContext, PointDataFacade)) { return false; }

		bOutputWeight = Settings->WeightToAttribute != EPCGExWeightOutputMode::NoOutput;
		bNormalizedWeight = Settings->WeightToAttribute != EPCGExWeightOutputMode::Raw;
		bOneMinusWeight = Settings->WeightToAttribute == EPCGExWeightOutputMode::NormalizedInverted || Settings->WeightToAttribute == EPCGExWeightOutputMode::NormalizedInvertedToDensity;

		if (Settings->WeightToAttribute == EPCGExWeightOutputMode::Raw)
		{
			WeightWriter = PointDataFacade->GetWritable<int32>(Settings->WeightAttributeName, PCGExData::EBufferInit::New);
		}
		else if (Settings->WeightToAttribute == EPCGExWeightOutputMode::Normalized)
		{
			NormalizedWeightWriter = PointDataFacade->GetWritable<double>(Settings->WeightAttributeName, PCGExData::EBufferInit::New);
		}

		if (Settings->OutputMode == EPCGExStagingOutputMode::Attributes)
		{
			bInherit = PointDataFacade->GetIn()->Metadata->HasAttribute(Settings->AssetPathAttributeName);
#if PCGEX_ENGINE_VERSION > 503
			PathWriter = PointDataFacade->GetWritable<FSoftObjectPath>(Settings->AssetPathAttributeName, bInherit ? PCGExData::EBufferInit::Inherit : PCGExData::EBufferInit::New);
#else
			PathWriter = PointDataFacade->GetWritable<FString>(Settings->AssetPathAttributeName, bInherit ? PCGExData::EBufferInit::Inherit : PCGExData::EBufferInit::New);
#endif
		}
		else
		{
			bInherit = PointDataFacade->GetIn()->Metadata->HasAttribute(PCGExStaging::Tag_EntryIdx);
			HashWriter = PointDataFacade->GetWritable<int64>(PCGExStaging::Tag_EntryIdx, bInherit ? PCGExData::EBufferInit::Inherit : PCGExData::EBufferInit::New);
		}

		StartParallelLoopForPoints();

		return true;
	}

	void FProcessor::PrepareLoopScopesForPoints(const TArray<PCGExMT::FScope>& Loops)
	{
		HighestSlotIndex = MakeShared<PCGExMT::TScopedValue<int8>>(Loops, -1);
	}

	void FProcessor::PrepareSingleLoopScopeForPoints(const PCGExMT::FScope& Scope)
	{
		PointDataFacade->Fetch(Scope);
		FilterScope(Scope);
	}

	void FProcessor::ProcessSinglePoint(const int32 Index, FPCGPoint& Point, const PCGExMT::FScope& Scope)
	{
		auto InvalidPoint = [&]()
		{
			if (bInherit) { return; }

			if (Settings->bPruneEmptyPoints)
			{
				Point.MetadataEntry = -2;
				return;
			}

			if (PathWriter)
			{
#if PCGEX_ENGINE_VERSION > 503
				PathWriter->GetMutable(Index) = FSoftObjectPath{};
#else
				PathWriter->GetMutable(Index) = TEXT("");
#endif
			}
			else
			{
				HashWriter->GetMutable(Index) = -1;
			}

			if (bOutputWeight)
			{
				if (WeightWriter) { WeightWriter->GetMutable(Index) = -1; }
				else if (NormalizedWeightWriter) { NormalizedWeightWriter->GetMutable(Index) = -1; }
			}

			if (Context->bPickMaterials) { MaterialPick[Index] = -1; }
		};

		if (!PointFilterCache[Index])
		{
			InvalidPoint();
			return;
		}

		const FPCGExAssetCollectionEntry* Entry = nullptr;
		const UPCGExAssetCollection* EntryHost = nullptr;

		const int32 Seed = PCGExRandom::GetSeedFromPoint(
			Helper->Details.SeedComponents, Point,
			Helper->Details.LocalSeed, Settings, Context->SourceComponent.Get());

		Helper->GetEntry(Entry, Index, Seed, EntryHost);

		if (!Entry || !Entry->Staging.Bounds.IsValid)
		{
			InvalidPoint();
			return;
		}

		if (Context->bPickMaterials)
		{
			if (Entry->MacroCache && Entry->MacroCache->GetType() == PCGExAssetCollection::EType::Mesh)
			{
				TSharedPtr<PCGExMeshCollection::FMacroCache> EntryMacroCache = StaticCastSharedPtr<PCGExMeshCollection::FMacroCache>(Entry->MacroCache);
				MaterialPick[Index] = EntryMacroCache->GetPickRandomWeighted(Seed);
				HighestSlotIndex->Set(Scope, FMath::Max(FMath::Max(0, EntryMacroCache->GetHighestIndex()), HighestSlotIndex->Get(Scope)));
				CachedPicks[Index] = Entry;
			}
			else
			{
				MaterialPick[Index] = -1;
			}
		}

		if (bOutputWeight)
		{
			double Weight = bNormalizedWeight ? static_cast<double>(Entry->Weight) / static_cast<double>(Context->MainCollection->LoadCache()->WeightSum) : Entry->Weight;
			if (bOneMinusWeight) { Weight = 1 - Weight; }
			if (WeightWriter) { WeightWriter->GetMutable(Index) = Weight; }
			else if (NormalizedWeightWriter) { NormalizedWeightWriter->GetMutable(Index) = Weight; }
			else { Point.Density = Weight; }
		}

		if (PathWriter)
		{
#if PCGEX_ENGINE_VERSION > 503
			PathWriter->GetMutable(Index) = Entry->Staging.Path;
#else
			PathWriter->GetMutable(Index) = Entry->Staging.Path.ToString();
#endif
		}
		else
		{
			HashWriter->GetMutable(Index) = Context->CollectionPickDatasetPacker->GetPickIdx(EntryHost, Entry->Staging.InternalIndex);
		}

		FBox OutBounds = Entry->Staging.Bounds;

		if (Variations.bEnabledBefore)
		{
			FPCGPoint ProxyPoint = Point;
			Variations.Apply(ProxyPoint, Entry->Variations, EPCGExVariationMode::Before);
			FittingHandler.ComputeTransform(Index, ProxyPoint, Point.Transform, OutBounds);
		}
		else
		{
			FittingHandler.ComputeTransform(Index, Point.Transform, OutBounds);
		}


		Point.BoundsMin = OutBounds.Min;
		Point.BoundsMax = OutBounds.Max;

		if (Variations.bEnabledAfter) { Variations.Apply(Point, Entry->Variations, EPCGExVariationMode::After); }
	}

	void FProcessor::CompleteWork()
	{
		if (Context->bPickMaterials)
		{
			int8 WriterCount = HighestSlotIndex->Flatten([](const int8 A, const int8 B) { return FMath::Max(A, B); }) + 1;
			if (Settings->MaxMaterialPicks > 0) { WriterCount = Settings->MaxMaterialPicks; }

			if (WriterCount > 0)
			{
				// Create writers
				// TODO : Optimize this -- right now it can potentially output tons of garbage when only an attribute with no entries would be enough

				MaterialWriters.Init(nullptr, WriterCount);

				for (int i = 0; i < WriterCount; i++)
				{
					const FName AttributeName = FName(FString::Printf(TEXT("%s_%d"), *Settings->MaterialAttributePrefix.ToString(), i));
#if PCGEX_ENGINE_VERSION > 503
					MaterialWriters[i] = PointDataFacade->GetWritable<FSoftObjectPath>(AttributeName, FSoftObjectPath(), true, PCGExData::EBufferInit::New);
#else
					MaterialWriters[i] = PointDataFacade->GetWritable<FString>(AttributeName, TEXT(""), true, PCGExData::EBufferInit::New);
#endif
				}

				StartParallelLoopForRange(NumPoints);
				return;
			}
			PCGE_LOG_C(Warning, GraphAndLog, Context, FTEXT("No material were picked -- no attribute will be written."));
		}

		PointDataFacade->Write(AsyncManager);
	}

	void FProcessor::ProcessSingleRangeIteration(const int32 Iteration, const PCGExMT::FScope& Scope)
	{
		const int32 Pick = MaterialPick[Iteration];
		if (Pick == -1 || PointDataFacade->GetMutablePoints()[Iteration].MetadataEntry == -2) { return; }

		const FPCGExMeshCollectionEntry* Entry = static_cast<const FPCGExMeshCollectionEntry*>(CachedPicks[Iteration]);
		if (Entry->MaterialVariants == EPCGExMaterialVariantsMode::None) { return; }
		if (Entry->MaterialVariants == EPCGExMaterialVariantsMode::Single)
		{
			if (!MaterialWriters.IsValidIndex(Entry->SlotIndex)) { return; }

#if PCGEX_ENGINE_VERSION > 503
			MaterialWriters[Entry->SlotIndex]->GetMutable(Iteration) = Entry->MaterialOverrideVariants[Pick].Material.ToSoftObjectPath();
#else
			MaterialWriters[Entry->SlotIndex]->GetMutable(Iteration) = Entry->MaterialOverrideVariants[Pick].Material.ToSoftObjectPath().ToString();
#endif
		}
		else if (Entry->MaterialVariants == EPCGExMaterialVariantsMode::Multi)
		{
			const FPCGExMaterialOverrideCollection& MEntry = Entry->MaterialOverrideVariantsList[Pick];
			for (int i = 0; i < MEntry.Overrides.Num(); i++)
			{
				const FPCGExMaterialOverrideEntry& SlotEntry = MEntry.Overrides[i];

				const int32 SlotIndex = SlotEntry.SlotIndex == -1 ? 0 : SlotEntry.SlotIndex;
				if (!MaterialWriters.IsValidIndex(SlotIndex)) { continue; }

#if PCGEX_ENGINE_VERSION > 503
				MaterialWriters[SlotIndex]->GetMutable(Iteration) = SlotEntry.Material.ToSoftObjectPath();
#else
				MaterialWriters[SlotIndex]->GetMutable(Iteration) = SlotEntry.Material.ToSoftObjectPath().ToString();
#endif
			}
		}
	}

	void FProcessor::OnRangeProcessingComplete()
	{
		PointDataFacade->Write(AsyncManager);
	}

	void FProcessor::Write()
	{
		// TODO : Find a better solution
		TArray<FPCGPoint>& MutablePoints = PointDataFacade->GetOut()->GetMutablePoints();

		int32 WriteIndex = 0;
		for (int32 i = 0; i < NumPoints; i++) { if (MutablePoints[i].MetadataEntry != -2) { MutablePoints[WriteIndex++] = MutablePoints[i]; } }

		MutablePoints.SetNum(WriteIndex);
	}
}

#undef LOCTEXT_NAMESPACE
#undef PCGEX_NAMESPACE
