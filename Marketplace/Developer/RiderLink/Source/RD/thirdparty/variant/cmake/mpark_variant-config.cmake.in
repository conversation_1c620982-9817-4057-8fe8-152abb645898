# MPark.Variant
#
# Copyright Michael <PERSON>, 2015-2017
#
# Distributed under the Boost Software License, Version 1.0.
# (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)

# Config file for MPark.Variant
#
#   `MPARK_VARIANT_INCLUDE_DIRS` - include directories
#   `MPARK_VARIANT_LIBRARIES`    - libraries to link against
#
# The following `IMPORTED` target is also defined:
#
#   `mpark_variant`

@PACKAGE_INIT@

include("${CMAKE_CURRENT_LIST_DIR}/mpark_variant-targets.cmake")

get_target_property(
  MPARK_VARIANT_INCLUDE_DIRS
  mpark_variant INTERFACE_INCLUDE_DIRECTORIES)

set_and_check(MPARK_VARIANT_INCLUDE_DIRS "${MPARK_VARIANT_INCLUDE_DIRS}")
set(MPARK_VARIANT_LIBRARIES mpark_variant)
