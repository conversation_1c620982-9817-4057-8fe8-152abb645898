// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "IVCamCoreEditorModule.h"

#include "UObject/WeakObjectPtr.h"
#include "UObject/WeakObjectPtrTemplates.h"

class UClass;
class UVCamWidget;
enum class ETransactionFilterResult : uint8;
struct FConcertTransactionFilterArgs;

namespace UE::VCamCoreEditor
{
	class FCompilationExtensionManager;

	class FVCamCoreEditorModule : public IVCamCoreEditorModule
	{
	public:

		static FVCamCoreEditorModule& Get()
		{
			return FModuleManager::Get().GetModuleChecked<FVCamCoreEditorModule>("VCamCoreEditor");
		}

		TSharedPtr<IConnectionRemapCustomization> CreateConnectionRemapCustomization(TSubclassOf<UVCamWidget> Class) const;

		//~ Begin IModuleInterface Interface
		virtual void StartupModule() override;
		virtual void ShutdownModule() override;
		//~ End IModuleInterface Interface
		
		//~ Begin IVCamCoreEditorModule Interface
		virtual void RegisterConnectionRemapCustomization(TSubclassOf<UVCamWidget> Class, FGetConnectionRemappingCustomization GetterDelegate) override;
		virtual void UnregisterConnectionRemapCustomization(TSubclassOf<UVCamWidget> Class) override;
		virtual uint32 GetAdvancedAssetCategoryForVCam() const override;
		//~ Begin IVCamCoreEditorModule Interface

	private:

		/** Maps pointer to UVCamWidget classes */
		TMap<TWeakObjectPtr<UClass>, FGetConnectionRemappingCustomization> ConnectionRemappingCustomizationFactories;

		TSharedPtr<FCompilationExtensionManager> CompilationExtensionManager;

		void RegisterAutoGeneratedDefaultEvents();
		void RegisterCustomizations();
		void RegisterDefaultConnectionRemappingCustomizations();
		
		void UnregisterCustomizations();
		
		void RegisterMultiUserFilters();
		ETransactionFilterResult ShouldObjectBeTransacted(const FConcertTransactionFilterArgs& FilterArgs) const;
	};
}