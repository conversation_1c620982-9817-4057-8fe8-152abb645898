{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "Storm Sync", "Description": "Sync, Pull, Push, asset dependencies.\n\nThis plugin is a recommended part of the Motion Design work flow.", "Category": "Experimental", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "CanContainContent": true, "IsBetaVersion": false, "IsExperimentalVersion": true, "Installed": false, "Modules": [{"Name": "StormSyncCore", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "<PERSON>", "Linux"]}, {"Name": "StormSyncDrives", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "<PERSON>", "Linux"]}, {"Name": "StormSyncEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "<PERSON>", "Linux"]}, {"Name": "StormSyncTests", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "<PERSON>", "Linux"]}, {"Name": "StormSyncTransportCore", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "<PERSON>", "Linux"]}, {"Name": "StormSyncTransportClient", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "<PERSON>", "Linux"]}, {"Name": "StormSyncTransportServer", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "<PERSON>", "Linux"]}, {"Name": "StormSyncImport", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "<PERSON>", "Linux"]}], "Plugins": [{"Name": "ContentBrowserAssetDataSource", "Enabled": true}]}