{"application/json": {"schema": {"$ref": "#/components/schemas/Pet"}, "examples": {"cat": {"summary": "An example of a cat", "value": {"name": "<PERSON><PERSON><PERSON>", "petType": "Cat", "color": "White", "gender": "male", "breed": "Persian"}}, "dog": {"summary": "An example of a dog with a cat's name", "value": {"name": "<PERSON><PERSON>", "petType": "Dog", "color": "Black", "gender": "Female", "breed": "Mixed"}, "frog": {"$ref": "#/components/examples/frog-example"}}}}}