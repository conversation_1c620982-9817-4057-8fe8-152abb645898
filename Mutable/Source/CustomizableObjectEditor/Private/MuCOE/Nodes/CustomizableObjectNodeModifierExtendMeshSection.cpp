// Copyright Epic Games, Inc. All Rights Reserved.

#include "MuCOE/Nodes/CustomizableObjectNodeModifierExtendMeshSection.h"

#include "MaterialTypes.h"
#include "MuCO/CustomizableObjectCustomVersion.h"
#include "MuCOE/CustomizableObjectEditor_Deprecated.h"
#include "MuCOE/CustomizableObjectGraph.h"
#include "MuCOE/EdGraphSchema_CustomizableObject.h"
#include "MuCOE/GraphTraversal.h"
#include "MuCOE/Nodes/CustomizableObjectNodeCopyMaterial.h"
#include "MuCOE/Nodes/CustomizableObjectNodeSkeletalMesh.h"
#include "MuCOE/Nodes/CustomizableObjectNodeTable.h"
#include "MuCOE/CustomizableObjectPin.h"

class UCustomizableObjectNode;
class UCustomizableObjectNodeRemapPins;

#define LOCTEXT_NAMESPACE "CustomizableObjectEditor"


void UCustomizableObjectNodeModifierExtendMeshSection::BackwardsCompatibleFixup(int32 CustomizableObjectCustomVersion)
{
	Super::BackwardsCompatibleFixup(CustomizableObjectCustomVersion);
	
	if (CustomizableObjectCustomVersion == FCustomizableObjectCustomVersion::ExtendMaterialRemoveImages)
	{
		const UCustomizableObjectNodeMaterialBase* ParentMaterial = GetCustomizableObjectExternalNode<UCustomizableObjectNodeMaterialBase>(ParentMaterialObject_DEPRECATED.Get(), ParentMaterialNodeId_DEPRECATED);
		if (ParentMaterial)
		{
			for (const FCustomizableObjectNodeExtendMaterialImage& Image : Images_DEPRECATED)
			{
				const UEdGraphPin* ImagePin = FindPin(Image.Name);
				if (!ImagePin)
				{
					continue;
				}

				FGuid ImageId = FGuid::NewGuid();

				// Search for the Image Id the Extend pin was referring to.
				const int32 NumImages = ParentMaterial->GetNumParameters(EMaterialParameterType::Texture);
				for (int32 ImageIndex = 0; ImageIndex < NumImages; ++ImageIndex)
				{
					if (ParentMaterial->GetParameterName(EMaterialParameterType::Texture, ImageIndex).ToString() == Image.Name)
					{
						ImageId = ParentMaterial->GetParameterId(EMaterialParameterType::Texture, ImageIndex).ParameterId;
						break;
					}
				}

				PinsParameter_DEPRECATED.Add(ImageId, FEdGraphPinReference(ImagePin));                
			}
		}

		Images_DEPRECATED.Empty();
	}

	if (CustomizableObjectCustomVersion == FCustomizableObjectCustomVersion::ConvertEditAndExtendToModifiers)
	{
		// Look for the parent material and set it as the modifier reference material 
		PinsParameterMap = PinsParameterMap_DEPRECATED;
		PinsParameterMap_DEPRECATED.Empty();

		UCustomizableObjectNode* ParentNode = GetCustomizableObjectExternalNode<UCustomizableObjectNode>(ParentMaterialObject_DEPRECATED.Get(), ParentMaterialNodeId_DEPRECATED);
		if (ParentNode)
		{
			// Add an autogenerated tag to the legacy parent, so that it can be referenced in this modifier.
			FString NewLegacyTag = MakeNodeAutoTag(ParentNode);
			RequiredTags.Add(NewLegacyTag);

			if (UCustomizableObjectNodeMaterial* MaterialParentNode = Cast<UCustomizableObjectNodeMaterial>(ParentNode))
			{
				ReferenceMaterial = MaterialParentNode->GetMaterial();
				MaterialParentNode->Tags.AddUnique(NewLegacyTag);
			}
			else if (UCustomizableObjectNodeModifierExtendMeshSection* ExtendParentNode = Cast<UCustomizableObjectNodeModifierExtendMeshSection>(ParentNode))
			{
				ReferenceMaterial = ExtendParentNode->ReferenceMaterial;
				ExtendParentNode->Tags.AddUnique(NewLegacyTag);
			}
			else
			{
				// Conversion failed?
				ensure(false);
				UE_LOG(LogMutable, Warning, TEXT("[%s] UCustomizableObjectNodeModifierExtendMeshSection version upgrade failed."), *GetOutermost()->GetName());
			}

			// If the tag was added to another CO, keep track of the addition to be able to repeat it in case
			// that CO is not re-saved but this one is.
			UCustomizableObject* ThisNodeObject = GetRootObject(*this);
			if (ParentMaterialObject_DEPRECATED != ThisNodeObject)
			{
				FLegacyTag LegacyTag;
				LegacyTag.ParentObject = ParentMaterialObject_DEPRECATED;
				LegacyTag.ParentNode = ParentMaterialNodeId_DEPRECATED;
				LegacyTag.Tag = NewLegacyTag;
				LegacyBackportsRequiredTags.AddUnique(LegacyTag);
			}
		}
		else
		{
			// No parent, no conversion.
			UE_LOG(LogMutable, Log, TEXT("[%s] UCustomizableObjectNodeModifierExtendMeshSection has no parent. It will not be upgraded."), *GetOutermost()->GetName());
		}

		ReconstructNode();
	}
}


void UCustomizableObjectNodeModifierExtendMeshSection::AllocateDefaultPins(UCustomizableObjectNodeRemapPins* RemapPins)
{
	const UEdGraphSchema_CustomizableObject* Schema = GetDefault<UEdGraphSchema_CustomizableObject>();

	CustomCreatePin(EGPD_Input, Schema->PC_Mesh, FName("Add Mesh") );

	// Begin texture pins
	{
		const int32 NumImages = GetNumParameters(EMaterialParameterType::Texture);
		for (int32 ImageIndex = 0; ImageIndex < NumImages; ++ImageIndex)
		{
			const FName ImageName = GetParameterName(EMaterialParameterType::Texture, ImageIndex);
			UEdGraphPin* PinImage = CustomCreatePin(EGPD_Input, Schema->PC_Image, ImageName);
			PinImage->bDefaultValueIsIgnored = true;

			const FNodeMaterialParameterId ImageId = GetParameterId(EMaterialParameterType::Texture, ImageIndex);
				
			PinsParameterMap.Add(ImageId, FEdGraphPinReference(PinImage));
		}
	}
	// End texture pins

	CustomCreatePin(EGPD_Output, Schema->PC_Modifier, TEXT("Modifier"));
}


FText UCustomizableObjectNodeModifierExtendMeshSection::GetNodeTitle(ENodeTitleType::Type TitleType) const
{
	return LOCTEXT("Extend_MeshSection", "Extend Mesh Section");
}


FString UCustomizableObjectNodeModifierExtendMeshSection::GetRefreshMessage() const
{
	return TEXT("The Reference Material has changed, texture channels might have been added, removed or renamed. Please refresh to reflect those changes.");
}


UEdGraphPin* UCustomizableObjectNodeModifierExtendMeshSection::AddMeshPin() const
{
	return FindPin(TEXT("Add Mesh"));
}


TArray<UCustomizableObjectLayout*> UCustomizableObjectNodeModifierExtendMeshSection::GetLayouts() const
{
	TArray<UCustomizableObjectLayout*> Result;

	if (UEdGraphPin* MeshPin = AddMeshPin())
	{
		if (const UEdGraphPin* ConnectedPin = FollowInputPin(*MeshPin))
		{
			if (const UEdGraphPin* SourceMeshPin = FindMeshBaseSource(*ConnectedPin, false))
			{
				if (const UCustomizableObjectNodeSkeletalMesh* MeshNode = Cast<UCustomizableObjectNodeSkeletalMesh>(SourceMeshPin->GetOwningNode()))
				{
					Result = MeshNode->GetLayouts(*SourceMeshPin);
				}
				else if (const UCustomizableObjectNodeTable* TableNode = Cast<UCustomizableObjectNodeTable>(SourceMeshPin->GetOwningNode()))
				{
					Result = TableNode->GetLayouts(SourceMeshPin);
				}
			}
		}
	}

	return Result;
}


FText UCustomizableObjectNodeModifierExtendMeshSection::GetTooltipText() const
{
	return LOCTEXT("Extend_Material_Tooltip", "Extend a mesh section: add a new mesh fragment, and add its corresponding texture to the modified material texture parameters.");
}


bool UCustomizableObjectNodeModifierExtendMeshSection::IsSingleOutputNode() const
{
	return true;
}


bool UCustomizableObjectNodeModifierExtendMeshSection::CustomRemovePin(UEdGraphPin& Pin)
{
	for (TMap<FNodeMaterialParameterId, FEdGraphPinReference>::TIterator It = PinsParameterMap.CreateIterator(); It; ++It)
	{
		if (It.Value().Get() == &Pin) // We could improve performance if FEdGraphPinReference exposed the pin id.
		{
			It.RemoveCurrent();
			break;
		}
	}

	return Super::CustomRemovePin(Pin);
}


TArray<FString>* UCustomizableObjectNodeModifierExtendMeshSection::GetEnableTags() 
{ 
	return &Tags; 
}


FString UCustomizableObjectNodeModifierExtendMeshSection::GetInternalTagDisplayName()
{
	UMaterialInterface* Material = ReferenceMaterial;
	return FString::Printf(TEXT("Extend Mesh [%s]"), Material ? *Material->GetName() : TEXT("no-material"));
}


#undef LOCTEXT_NAMESPACE
