// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "HAL/Platform.h"
#include "Containers/Map.h"
#include "Containers/Array.h"
#include "Containers/UnrealString.h"

#include "Types.generated.h"

/** This type is used in some mesh clip operations. 
* Warning: this type is used compiled COs. Any relevant change to the option order requires and update in the CustomizableObjectPrivate::ECustomizableObjectVersions
*/
UENUM()
enum class EFaceCullStrategy : uint8
{
	AllVerticesCulled = 0 UMETA(DisplayName = "Remove face if all vertices removed"),
	OneVertexCulled = 1 UMETA(DisplayName = "Remove face if one vertex removed"),
};


/** This type is used in some mesh clip operations.
* Warning: this type is used compiled COs. Any relevant change to the option order requires and update in the CustomizableObjectPrivate::ECustomizableObjectVersions
*/
enum class EClipVertexSelectionType : uint8
{
	None = 0,
	Shape = 1,
	BoneHierarchy = 2,
};


namespace mu
{
	class OutputArchive;
	class InputArchive;

	/** Handle used to identify separate resources generated by mutable: images and meshes included in instances. */
    typedef uint64 FResourceID;

	enum class EMemoryInitPolicy
	{
		Uninitialized,
		Zeroed,
	};

}


//
template<typename T>
inline uint32 GetTypeHash(const TArray<T>& Key)
{
	uint32 Hash = 0;
	for (const T& e : Key)
	{
		Hash = HashCombine(Hash, GetTypeHash(e));
	}
	return Hash;
}


//
template<typename K, typename V>
inline bool operator==(const TMap<K,V>& A, const TMap<K,V>& B )
{	
	return A.OrderIndependentCompareEqual(B);
}


